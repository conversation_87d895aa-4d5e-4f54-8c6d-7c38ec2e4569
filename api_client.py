#!/usr/bin/env python3
"""
Basic API client for Datto Partner API
"""
import httpx
import base64
import asyncio
import sys
from typing import Optional, Dict, Any
from datetime import datetime
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# IMPORTANT: Redirect all print statements to stderr
_print = print
def safe_print(*args, **kwargs):
    """Override print to always go to stderr"""
    kwargs['file'] = sys.stderr
    return _print(*args, **kwargs)

print = safe_print


class DattoAPIClient:
    """Simple API client with authentication and retry logic"""
    
    def __init__(self, public_key: str, secret_key: str, debug: bool = False):
        self.public_key = public_key
        self.secret_key = secret_key
        self.base_url = 'https://api.datto.com'
        self.debug = debug
        
        # Rate limiting tracking
        self.rate_limit_remaining = 10000
        self.rate_limit_reset = 0
        self.request_count = 0
        
        # HTTP client
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            follow_redirects=True,
            headers={
                'User-Agent': 'Datto-API-MCP/1.0',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        )
    
    def _create_auth_header(self) -> str:
        """Create Basic Auth header"""
        credentials = f"{self.public_key}:{self.secret_key}"
        encoded = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded}"
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type((httpx.TimeoutException, httpx.NetworkError))
    )
    async def request(
        self,
        method: str,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        timeout: float = 30.0
    ) -> httpx.Response:
        """Make HTTP request with retries"""
        url = f"{self.base_url}{path}"
        
        # Add default pagination for GET requests
        if method == 'GET' and params is None:
            params = {'_page': 1, '_perPage': 50}
        
        headers = {'Authorization': self._create_auth_header()}
        
        if self.debug:
            print(f"[DEBUG] {method} {url} (params: {params})")
        
        start_time = datetime.now()
        
        response = await self.client.request(
            method=method,
            url=url,
            params=params,
            json=json_data,
            headers=headers,
            timeout=timeout
        )
        
        elapsed_ms = (datetime.now() - start_time).total_seconds() * 1000
        
        # Update rate limit info
        if 'X-API-Limit-Remaining' in response.headers:
            self.rate_limit_remaining = int(response.headers['X-API-Limit-Remaining'])
        if 'X-API-Limit-Resets' in response.headers:
            self.rate_limit_reset = int(response.headers['X-API-Limit-Resets'])
        
        self.request_count += 1
        
        if self.debug:
            print(f"[DEBUG] Response: {response.status_code} ({elapsed_ms:.0f}ms)")
            print(f"[DEBUG] Rate limit remaining: {self.rate_limit_remaining}")
        
        # Handle rate limiting
        if response.status_code == 429 or self.rate_limit_remaining < 100:
            wait_time = max(self.rate_limit_reset - datetime.now().timestamp(), 60)
            print(f"[RATE LIMIT] Waiting {wait_time}s...")
            await asyncio.sleep(wait_time)
        
        return response
    
    async def get(self, path: str, params: Optional[Dict[str, Any]] = None) -> httpx.Response:
        """Convenience method for GET requests"""
        return await self.request('GET', path, params=params)
    
    async def post(self, path: str, json_data: Dict[str, Any], params: Optional[Dict[str, Any]] = None) -> httpx.Response:
        """Convenience method for POST requests"""
        return await self.request('POST', path, params=params, json_data=json_data)
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    async def __aenter__(self):
        """Async context manager support"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Cleanup on context exit"""
        await self.close()


class APIError(Exception):
    """API-specific errors"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_text: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text