#!/usr/bin/env python3
"""
Standalone test script for Datto API - No MCP required
"""
import asyncio
import os
from dotenv import load_dotenv
from api_client import DattoAP<PERSON>lient
from debug_logger import DebugLogger

# Load environment variables
load_dotenv()


async def test_basic_connectivity():
    """Test 1: Basic API connectivity"""
    logger = DebugLogger("test_basic", "test_basic.log", verbose=True)
    
    # Get credentials
    public_key = os.getenv('DATTO_PUBLIC_KEY')
    secret_key = os.getenv('DATTO_SECRET_KEY')
    
    if not public_key or not secret_key:
        logger.error("Missing API credentials in .env file")
        logger.error("Please ensure you have:")
        logger.error("  DATTO_PUBLIC_KEY=your_public_key")
        logger.error("  DATTO_SECRET_KEY=your_secret_key")
        return False
    
    logger.log_phase("TEST 1", "Basic Connectivity")
    
    async with DattoAPIClient(public_key, secret_key, debug=True) as client:
        try:
            # Test device list
            response = await client.get("/v1/bcdr/device", {"_page": 1, "_perPage": 5})
            
            logger.log_request(
                "GET", "/v1/bcdr/device", 
                response.status_code, 
                0  # We'd track actual time in real implementation
            )
            
            if response.status_code == 200:
                data = response.json()
                devices = data.get('items', [])
                logger.log_discovery("device", len(devices))
                
                if devices:
                    logger.info(f"\nFirst device:")
                    logger.info(f"  Serial: {devices[0].get('serialNumber')}")
                    logger.info(f"  Name: {devices[0].get('name')}")
                    logger.info(f"  Model: {devices[0].get('model')}")
                
                return True
            else:
                logger.error(f"Failed with status {response.status_code}")
                logger.error(f"Response: {response.text[:200]}")
                return False
                
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return False


async def test_resource_discovery():
    """Test 2: Resource discovery"""
    from resource_discovery import ResourceDiscovery
    
    logger = DebugLogger("test_discovery", "test_discovery.log", verbose=True)
    
    public_key = os.getenv('DATTO_PUBLIC_KEY')
    secret_key = os.getenv('DATTO_SECRET_KEY')
    
    if not public_key or not secret_key:
        logger.error("Missing API credentials")
        return
    
    logger.log_phase("TEST 2", "Resource Discovery")
    
    async with DattoAPIClient(public_key, secret_key) as client:
        discovery = ResourceDiscovery(client)
        
        # Test individual discovery functions
        logger.info("Testing device discovery...")
        devices = await discovery.discover_devices()
        logger.log_discovery("device", len(devices))
        
        if devices:
            logger.info("\nSample devices:")
            for device in devices[:3]:
                logger.info(f"  - {device.resource_id}: {device.name}")
        
        # Test agent discovery
        logger.info("\nTesting agent discovery...")
        agents = await discovery.discover_agents()
        logger.log_discovery("agent", len(agents))
        
        # Test relationship discovery
        if devices:
            logger.info("\nTesting relationship discovery...")
            await discovery.discover_relationships()
            assets = discovery.resources.get('asset', [])
            logger.log_discovery("asset", len(assets))
        
        # Summary
        logger.log_summary({
            "total_resources": sum(len(r) for r in discovery.resources.values()),
            "resource_types": {
                rtype: len(resources) 
                for rtype, resources in discovery.resources.items()
            }
        })


async def test_endpoint_testing():
    """Test 3: Endpoint testing with discovered resources"""
    from resource_discovery import ResourceDiscovery
    from endpoint_tester import EndpointTester
    
    logger = DebugLogger("test_endpoints", "test_endpoints.log", verbose=True)
    
    public_key = os.getenv('DATTO_PUBLIC_KEY')
    secret_key = os.getenv('DATTO_SECRET_KEY')
    
    if not public_key or not secret_key:
        logger.error("Missing API credentials")
        return
    
    logger.log_phase("TEST 3", "Endpoint Testing")
    
    async with DattoAPIClient(public_key, secret_key) as client:
        # First discover resources
        logger.info("Discovering resources...")
        discovery = ResourceDiscovery(client)
        resources = await discovery.discover_all()
        
        # Then test endpoints
        tester = EndpointTester(client)
        
        # Test a simple endpoint
        logger.info("\nTesting GET /v1/bcdr/device...")
        result = await tester.test_endpoint("GET", "/v1/bcdr/device")
        logger.log_test_result("/v1/bcdr/device", result.status.value == "success", {
            "status": result.status.value,
            "status_code": result.status_code,
            "response_time_ms": result.response_time_ms
        })
        
        # Test an endpoint with parameters
        if 'device' in resources and resources['device']:
            device = resources['device'][0]
            logger.info(f"\nTesting device assets for {device.resource_id}...")
            
            result = await tester.test_endpoint(
                "GET",
                "/v1/bcdr/device/{serialNumber}/asset",
                path_params={"serialNumber": device.resource_id}
            )
            
            logger.log_test_result(
                f"/v1/bcdr/device/{device.resource_id}/asset",
                result.status.value == "success",
                {
                    "status": result.status.value,
                    "has_screenshot_data": result.has_screenshot_data,
                    "fields_discovered": len(result.discovered_fields)
                }
            )
            
            if result.has_screenshot_data:
                logger.info("  ✓ Screenshot data detected!")
        
        # Test undocumented endpoints
        logger.info("\nDiscovering undocumented endpoints...")
        discovered = await tester.discover_undocumented_endpoints(resources)
        logger.info(f"Found {len(discovered)} undocumented endpoints")
        
        # Summary
        summary = tester.get_summary()
        logger.log_summary(summary)


async def test_schema_analysis():
    """Test 4: Schema extraction"""
    from schema_analyzer import SchemaAnalyzer
    
    logger = DebugLogger("test_schema", "test_schema.log", verbose=True)
    
    logger.log_phase("TEST 4", "Schema Analysis")
    
    # Test with sample data
    sample_data = {
        "serialNumber": "ABC123",
        "name": "Test Device",
        "model": "SIRIS",
        "lastSeen": 1700000000,
        "online": True,
        "agents": [
            {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "hostname": "SERVER01",
                "lastBackup": 1699999999,
                "protectedVolumes": ["C:", "D:"],
                "lastScreenshotUrl": "https://example.com/screenshot.png"
            }
        ]
    }
    
    analyzer = SchemaAnalyzer()
    
    # Extract schema
    schema = analyzer.extract_schema(sample_data)
    logger.info("Extracted schema:")
    import json
    logger.info(json.dumps(schema, indent=2))
    
    # Get all fields
    fields = analyzer.get_all_fields(sample_data)
    logger.info(f"\nDiscovered {len(fields)} fields:")
    for field in sorted(fields):
        logger.info(f"  - {field}")
    
    # Detect special fields
    special = analyzer.detect_special_fields(sample_data)
    logger.info("\nSpecial fields detected:")
    for field_type, detected in special.items():
        if detected:
            logger.info(f"  ✓ {field_type}")


async def run_all_tests():
    """Run all tests in sequence"""
    print("\n" + "="*80)
    print("DATTO API STANDALONE TEST SUITE")
    print("="*80 + "\n")
    
    tests = [
        ("Basic Connectivity", test_basic_connectivity),
        ("Resource Discovery", test_resource_discovery),
        ("Endpoint Testing", test_endpoint_testing),
        ("Schema Analysis", test_schema_analysis)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        print("-" * 40)
        
        try:
            result = await test_func()
            results[test_name] = "PASSED" if result != False else "FAILED"
        except Exception as e:
            print(f"ERROR: {e}")
            results[test_name] = "ERROR"
        
        await asyncio.sleep(1)  # Small delay between tests
    
    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    for test_name, result in results.items():
        status_icon = "✓" if result == "PASSED" else "✗"
        print(f"{status_icon} {test_name}: {result}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        
        if test_name == "basic":
            asyncio.run(test_basic_connectivity())
        elif test_name == "discovery":
            asyncio.run(test_resource_discovery())
        elif test_name == "endpoints":
            asyncio.run(test_endpoint_testing())
        elif test_name == "schema":
            asyncio.run(test_schema_analysis())
        else:
            print(f"Unknown test: {test_name}")
            print("Available tests: basic, discovery, endpoints, schema")
    else:
        # Run all tests
        asyncio.run(run_all_tests())