#!/usr/bin/env python3
"""
Debug logging utilities for Datto API testing
"""
import logging
import sys
import json
from datetime import datetime
from typing import Any, Dict, Optional


class DebugLogger:
    """Enhanced logger for debugging API operations"""
    
    def __init__(self, name: str, log_file: Optional[str] = None, verbose: bool = False):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG if verbose else logging.INFO)
        self.logger.handlers = []  # Clear existing handlers
        
        # Console handler with color
        console = logging.StreamHandler(sys.stdout)
        console.setLevel(logging.INFO)
        
        # Color formatter for console
        console_formatter = ColorFormatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%H:%M:%S'
        )
        console.setFormatter(console_formatter)
        self.logger.addHandler(console)
        
        # File handler for detailed logs
        if log_file:
            file_handler = logging.FileHandler(log_file, mode='a')
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
            
            # Log separator for new session
            file_handler.stream.write(f"\n{'='*80}\n")
            file_handler.stream.write(f"New session started: {datetime.now()}\n")
            file_handler.stream.write(f"{'='*80}\n\n")
            file_handler.stream.flush()
    
    def log_request(self, method: str, url: str, status: int, time_ms: float, 
                   params: Optional[Dict] = None, error: Optional[str] = None):
        """Log API request with details"""
        if error:
            self.logger.error(f"{method} {url} -> ERROR: {error}")
        elif status >= 400:
            self.logger.warning(f"{method} {url} -> {status} ({time_ms:.0f}ms)")
        else:
            self.logger.info(f"{method} {url} -> {status} ({time_ms:.0f}ms)")
        
        if params:
            self.logger.debug(f"  Params: {json.dumps(params, indent=2)}")
    
    def log_discovery(self, resource_type: str, count: int, details: Optional[str] = None):
        """Log resource discovery"""
        self.logger.info(f"Discovered {count} {resource_type}(s)")
        if details:
            self.logger.debug(f"  Details: {details}")
    
    def log_test_result(self, endpoint: str, success: bool, details: Optional[Dict] = None):
        """Log endpoint test result"""
        if success:
            self.logger.info(f"✓ {endpoint} - SUCCESS")
        else:
            self.logger.error(f"✗ {endpoint} - FAILED")
        
        if details:
            self.logger.debug(f"  Details: {json.dumps(details, indent=2)}")
    
    def log_phase(self, phase: str, description: str):
        """Log test phase"""
        self.logger.info(f"\n{'='*60}")
        self.logger.info(f"{phase}: {description}")
        self.logger.info(f"{'='*60}\n")
    
    def log_summary(self, summary: Dict[str, Any]):
        """Log test summary"""
        self.logger.info("\n" + "="*60)
        self.logger.info("TEST SUMMARY")
        self.logger.info("="*60)
        
        for key, value in summary.items():
            if isinstance(value, dict):
                self.logger.info(f"{key}:")
                for sub_key, sub_value in value.items():
                    self.logger.info(f"  {sub_key}: {sub_value}")
            else:
                self.logger.info(f"{key}: {value}")
    
    def debug(self, message: str):
        self.logger.debug(message)
    
    def info(self, message: str):
        self.logger.info(message)
    
    def warning(self, message: str):
        self.logger.warning(message)
    
    def error(self, message: str):
        self.logger.error(message)


class ColorFormatter(logging.Formatter):
    """Custom formatter with colors for different log levels"""
    
    # ANSI color codes
    grey = "\x1b[38;20m"
    green = "\x1b[32;20m"
    yellow = "\x1b[33;20m"
    red = "\x1b[31;20m"
    bold_red = "\x1b[31;1m"
    reset = "\x1b[0m"
    
    FORMATS = {
        logging.DEBUG: grey + "%(message)s" + reset,
        logging.INFO: green + "%(message)s" + reset,
        logging.WARNING: yellow + "%(message)s" + reset,
        logging.ERROR: red + "%(message)s" + reset,
        logging.CRITICAL: bold_red + "%(message)s" + reset
    }
    
    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno, "%(message)s")
        formatter = logging.Formatter(log_fmt, datefmt=self.datefmt)
        return formatter.format(record)