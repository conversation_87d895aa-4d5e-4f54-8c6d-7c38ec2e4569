#!/usr/bin/env python3
"""
Endpoint testing module for Datto API
"""
import sys
from typing import Dict, Any, List, Optional
from datetime import datetime
from api_client import DattoAPIClient
from models import EndpointResult, EndpointStatus
from schema_analyzer import SchemaAnalyzer

# IMPORTANT: Redirect all print statements to stderr
_print = print
def safe_print(*args, **kwargs):
    """Override print to always go to stderr"""
    kwargs['file'] = sys.stderr
    return _print(*args, **kwargs)

print = safe_print


class EndpointTester:
    """Tests API endpoints with various strategies"""
    
    def __init__(self, client: DattoAPIClient):
        self.client = client
        self.schema_analyzer = SchemaAnalyzer()
        self.results: List[EndpointResult] = []
    
    async def test_endpoint(
        self,
        method: str,
        path: str,
        path_params: Optional[Dict[str, Any]] = None,
        query_params: Optional[Dict[str, Any]] = None,
        body: Optional[Dict[str, Any]] = None
    ) -> EndpointResult:
        """Test a single endpoint"""
        result = EndpointResult(path=path, method=method, status=EndpointStatus.UNTESTED)
        
        # Replace path parameters
        test_path = path
        if path_params:
            for param, value in path_params.items():
                test_path = test_path.replace(f'{{{param}}}', str(value))
        
        try:
            start_time = datetime.now()
            
            response = await self.client.request(
                method=method,
                path=test_path,
                params=query_params,
                json_data=body
            )
            
            response_time = (datetime.now() - start_time).total_seconds() * 1000
            
            result.status_code = response.status_code
            result.response_time_ms = response_time
            result.headers = dict(response.headers)
            
            if response.status_code in [200, 201]:
                # Success!
                data = response.json()
                result.status = EndpointStatus.SUCCESS
                result.response_data = data
                result.working_params = path_params or {}
                
                # Extract schema
                result.response_schema = self.schema_analyzer.extract_schema(data)
                result.discovered_fields = self.schema_analyzer.get_all_fields(data)
                
                # Detect special fields
                special_fields = self.schema_analyzer.detect_special_fields(data)
                result.has_screenshot_data = special_fields['has_screenshot_data']
                result.has_verification_data = special_fields['has_verification_data']
                
            elif response.status_code == 401:
                result.status = EndpointStatus.AUTH_FAILED
                result.error_message = "Authentication failed"
            elif response.status_code == 404:
                result.status = EndpointStatus.NOT_FOUND
                result.error_message = "Endpoint not found"
            elif response.status_code == 429:
                result.status = EndpointStatus.RATE_LIMITED
                result.error_message = "Rate limit exceeded"
            else:
                result.status = EndpointStatus.ERROR
                result.error_message = f"HTTP {response.status_code}: {response.text[:200]}"
                
        except Exception as e:
            result.status = EndpointStatus.ERROR
            result.error_message = str(e)
        
        self.results.append(result)
        return result
    
    async def test_endpoint_with_resources(
        self,
        path: str,
        method: str,
        resources: Dict[str, List[Any]]
    ) -> List[EndpointResult]:
        """Test endpoint with discovered resources"""
        results = []
        
        # Extract parameter names from path
        import re
        param_matches = re.findall(r'\{(\w+)\}', path)
        
        if not param_matches:
            # No parameters needed
            result = await self.test_endpoint(method, path)
            results.append(result)
        else:
            # Try to match parameters with resources
            test_combinations = self._generate_test_combinations(param_matches, resources)
            
            # Test up to 3 combinations
            for combo in test_combinations[:3]:
                result = await self.test_endpoint(
                    method=method,
                    path=path,
                    path_params=combo
                )
                results.append(result)
                
                # Stop on first success
                if result.status == EndpointStatus.SUCCESS:
                    break
        
        return results
    
    def _generate_test_combinations(
        self,
        param_names: List[str],
        resources: Dict[str, List[Any]]
    ) -> List[Dict[str, str]]:
        """Generate parameter combinations from resources"""
        combinations = []
        
        # Map parameter names to resource types
        param_mapping = {
            'serialNumber': 'device',
            'deviceSerial': 'device',
            'clientId': 'client',
            'organizationId': 'client',
            'assetUuid': 'asset',
            'assetId': 'asset',
            'agentUuid': 'agent',
            'agentId': 'agent',
            'saasCustomerId': 'saas_domain',
            'volumeName': 'volume',
            'volume': 'volume'
        }
        
        # Generate combinations
        combo = {}
        for param_name in param_names:
            resource_type = param_mapping.get(param_name)
            
            if resource_type and resource_type in resources:
                resource_list = resources[resource_type]
                if resource_list:
                    # Use first available resource
                    resource = resource_list[0]
                    combo[param_name] = resource.resource_id
        
        if combo:
            combinations.append(combo)
        
        return combinations
    
    async def discover_undocumented_endpoints(
        self,
        resources: Dict[str, List[Any]]
    ) -> List[EndpointResult]:
        """Try common undocumented endpoint patterns"""
        patterns = [
            # Screenshot patterns
            '/v1/bcdr/device/{serialNumber}/screenshot',
            '/v1/bcdr/device/{serialNumber}/asset/{assetId}/screenshot',
            '/v1/bcdr/device/{serialNumber}/asset/{assetId}/screenshot/latest',
            
            # Verification patterns
            '/v1/bcdr/device/{serialNumber}/verification',
            '/v1/bcdr/device/{serialNumber}/asset/{assetId}/verification',
            
            # Backup patterns
            '/v1/bcdr/device/{serialNumber}/backups',
            '/v1/bcdr/device/{serialNumber}/asset/{assetId}/backups',
            
            # Status patterns
            '/v1/bcdr/device/{serialNumber}/status',
            '/v1/bcdr/device/{serialNumber}/health',
            
            # General patterns
            '/v1/status',
            '/v1/health',
            '/v1/user'
        ]
        
        discovered = []
        
        for pattern in patterns:
            # Skip if already tested
            if any(pattern == r.path for r in self.results):
                continue
            
            print(f"\n[DISCOVER] Trying: {pattern}")
            
            results = await self.test_endpoint_with_resources(
                pattern, 'GET', resources
            )
            
            for result in results:
                if result.status == EndpointStatus.SUCCESS:
                    print(f"[DISCOVER] Found undocumented endpoint!")
                    discovered.append(result)
                    break
        
        return discovered
    
    def get_summary(self) -> Dict[str, Any]:
        """Get testing summary"""
        total = len(self.results)
        by_status = {}
        
        for result in self.results:
            status = result.status.value
            by_status[status] = by_status.get(status, 0) + 1
        
        return {
            'total_tested': total,
            'by_status': by_status,
            'success_rate': by_status.get('success', 0) / total * 100 if total > 0 else 0,
            'screenshot_endpoints': len([r for r in self.results if r.has_screenshot_data]),
            'verification_endpoints': len([r for r in self.results if r.has_verification_data])
        }