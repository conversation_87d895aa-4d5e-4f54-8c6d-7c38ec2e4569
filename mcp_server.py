#!/usr/bin/env python3
"""
Simplified MCP Server for Datto API using modular components
"""
import asyncio
import json
import os
import sys
from typing import Dict, Any, List
from dotenv import load_dotenv

# MCP imports
from mcp.server.lowlevel import Server, NotificationOptions
from mcp.server.models import InitializationOptions
import mcp.server.stdio
from mcp.types import <PERSON><PERSON>, Text<PERSON>ontent, CallToolResult

# Our modular components
from api_client import DattoAPIClient
from resource_discovery import ResourceDiscovery
from endpoint_tester import EndpointTester
from schema_analyzer import SchemaAnalyzer
from models import EndpointStatus

# Load environment variables
load_dotenv()

# IMPORTANT: Redirect all print statements to stderr
_print = print
def safe_print(*args, **kwargs):
    """Override print to always go to stderr"""
    kwargs['file'] = sys.stderr
    return _print(*args, **kwargs)

print = safe_print


class SimpleDattoMCPServer:
    """Simplified MCP Server using modular components"""
    
    def __init__(self):
        try:
            self.server = Server("datto-api-tools")
            self.client = None
            self.resources = {}
            self.test_results = []
            
            # Get credentials from environment
            self.public_key = os.getenv('DATTO_PUBLIC_KEY')
            self.secret_key = os.getenv('DATTO_SECRET_KEY')
            
            self.setup_handlers()
        except Exception as e:
            print(f"Failed to initialize server: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            raise
    
    def _create_response(self, text: str):
        """Create response that works with this MCP version"""
        # Try to create a proper response using explicit construction
        try:
            # Create TextContent object properly
            from mcp.types import TextContent
            content = TextContent(type="text", text=text)
            return [content]
        except Exception as e:
            print(f"TextContent creation failed: {e}", file=sys.stderr)
            # Try returning a list with a dictionary
            try:
                return [{"type": "text", "text": text}]
            except Exception as e2:
                print(f"Dict creation failed: {e2}", file=sys.stderr)
                # Last resort - return as list of one string
                return [text]
    
    def setup_handlers(self):
        """Setup MCP handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools():
            return [
                Tool(
                    name="test_connection",
                    description="Test basic API connectivity",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                Tool(
                    name="discover_resources",
                    description="Discover available API resources",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "resource_types": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific resource types to discover (optional)"
                            }
                        }
                    }
                ),
                Tool(
                    name="test_endpoint",
                    description="Test a specific API endpoint",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "method": {
                                "type": "string",
                                "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"]
                            },
                            "path": {
                                "type": "string",
                                "description": "API path (e.g., /v1/bcdr/device)"
                            },
                            "params": {
                                "type": "object",
                                "description": "Path or query parameters"
                            },
                            "body": {
                                "type": "object",
                                "description": "Request body for POST/PUT/PATCH"
                            }
                        },
                        "required": ["method", "path"]
                    }
                ),
                Tool(
                    name="analyze_schema",
                    description="Analyze the schema of API responses",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "endpoint_index": {
                                "type": "integer",
                                "description": "Index of previously tested endpoint to analyze"
                            }
                        }
                    }
                ),
                Tool(
                    name="get_summary",
                    description="Get summary of all API testing",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                )
            ]
        
        @self.server.list_resources()
        async def handle_list_resources():
            """Handle resources/list requests"""
            return []
        
        @self.server.list_prompts()
        async def handle_list_prompts():
            """Handle prompts/list requests"""
            return []
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: dict):
            if not self.public_key or not self.secret_key:
                response = self._create_response("Error: API credentials not found in environment variables")
                print(f"[DEBUG] Response type: {type(response)}", file=sys.stderr)
                print(f"[DEBUG] Response content: {response}", file=sys.stderr)
                return response
            
            # Initialize client if needed
            if not self.client:
                self.client = DattoAPIClient(self.public_key, self.secret_key)
            
            if arguments is None:
                arguments = {}
            
            try:
                if name == "test_connection":
                    result = await self._test_connection()
                elif name == "discover_resources":
                    result = await self._discover_resources(arguments)
                elif name == "test_endpoint":
                    result = await self._test_endpoint(arguments)
                elif name == "analyze_schema":
                    result = await self._analyze_schema(arguments)
                elif name == "get_summary":
                    result = await self._get_summary()
                else:
                    result = f"Unknown tool: {name}"
                
                response = self._create_response(result)
                print(f"[DEBUG] Response type: {type(response)}", file=sys.stderr)
                print(f"[DEBUG] Response length: {len(response) if hasattr(response, '__len__') else 'N/A'}", file=sys.stderr)
                return response
                
            except Exception as e:
                response = self._create_response(f"Error: {str(e)}")
                print(f"[DEBUG] Error response type: {type(response)}", file=sys.stderr)
                return response
    
    async def _test_connection(self) -> str:
        """Test basic connectivity"""
        try:
            response = await self.client.get("/v1/bcdr/device", {"_perPage": 1})
            
            if response.status_code == 200:
                return f"""# Connection Test: SUCCESS ✓

**Status**: Connected successfully
**Rate Limit Remaining**: {self.client.rate_limit_remaining}
**API Base URL**: {self.client.base_url}

Connection to Datto API is working properly."""
            else:
                return f"""# Connection Test: FAILED ✗

**Status Code**: {response.status_code}
**Error**: {response.text[:200]}

Please check your API credentials."""
                
        except Exception as e:
            return f"""# Connection Test: ERROR

**Error**: {str(e)}

Unable to connect to the API."""
    
    async def _discover_resources(self, args: Dict[str, Any]) -> str:
        """Discover resources"""
        discovery = ResourceDiscovery(self.client)
        
        resource_types = args.get('resource_types')
        
        if resource_types:
            # Discover specific types
            for rtype in resource_types:
                if rtype == "device":
                    self.resources['device'] = await discovery.discover_devices()
                elif rtype == "agent":
                    self.resources['agent'] = await discovery.discover_agents()
        else:
            # Discover all
            self.resources = await discovery.discover_all()
        
        # Build summary
        result = "# Resource Discovery Results\n\n"
        
        total = 0
        for rtype, resources in self.resources.items():
            if resources:
                result += f"## {rtype} ({len(resources)} found)\n"
                for r in resources[:3]:
                    result += f"- **{r.resource_id}**: {r.name or 'No name'}\n"
                if len(resources) > 3:
                    result += f"- ... and {len(resources) - 3} more\n"
                result += "\n"
                total += len(resources)
        
        result += f"\n**Total Resources**: {total}"
        
        return result
    
    async def _test_endpoint(self, args: Dict[str, Any]) -> str:
        """Test an endpoint"""
        tester = EndpointTester(self.client)
        
        method = args['method']
        path = args['path']
        params = args.get('params', {})
        body = args.get('body')
        
        # Auto-fill parameters if possible
        import re
        param_matches = re.findall(r'\{(\w+)\}', path)
        if param_matches and not params:
            # Try to use discovered resources
            for param in param_matches:
                if param == "serialNumber" and 'device' in self.resources:
                    if self.resources['device']:
                        params[param] = self.resources['device'][0].resource_id
        
        # Test the endpoint
        result = await tester.test_endpoint(
            method=method,
            path=path,
            path_params=params if param_matches else None,
            query_params=params if not param_matches else None,
            body=body
        )
        
        self.test_results.append(result)
        
        # Build response
        response = f"""# Endpoint Test Result

**Endpoint**: `{method} {path}`
**Status**: {result.status.value}
"""
        
        if result.status_code:
            response += f"**Status Code**: {result.status_code}\n"
        
        if result.response_time_ms:
            response += f"**Response Time**: {result.response_time_ms:.0f}ms\n"
        
        if result.status == EndpointStatus.SUCCESS:
            response += f"\n## Success Details\n"
            response += f"- **Fields Discovered**: {len(result.discovered_fields)}\n"
            
            if result.has_screenshot_data:
                response += "- ✓ Screenshot data detected\n"
            if result.has_verification_data:
                response += "- ✓ Verification data detected\n"
            
            if result.response_data:
                response += f"\n## Sample Response\n```json\n"
                if isinstance(result.response_data, dict):
                    sample = dict(list(result.response_data.items())[:5])
                    response += json.dumps(sample, indent=2)
                else:
                    response += str(result.response_data)[:500]
                response += "\n```"
        else:
            response += f"\n## Error\n{result.error_message}"
        
        return response
    
    async def _analyze_schema(self, args: Dict[str, Any]) -> str:
        """Analyze response schema"""
        index = args.get('endpoint_index', -1)
        
        if not self.test_results:
            return "No endpoints have been tested yet"
        
        if index >= len(self.test_results):
            return f"Invalid index. Only {len(self.test_results)} endpoints tested"
        
        result = self.test_results[index]
        
        if not result.response_data:
            return "No response data to analyze"
        
        analyzer = SchemaAnalyzer()
        schema = analyzer.extract_schema(result.response_data)
        fields = analyzer.get_all_fields(result.response_data)
        special = analyzer.detect_special_fields(result.response_data)
        
        response = f"""# Schema Analysis

**Endpoint**: `{result.method} {result.path}`

## OpenAPI Schema
```json
{json.dumps(schema, indent=2)}
```

## Discovered Fields ({len(fields)} total)
"""
        
        for field in sorted(list(fields))[:20]:
            response += f"- {field}\n"
        
        if len(fields) > 20:
            response += f"- ... and {len(fields) - 20} more\n"
        
        response += "\n## Special Fields Detected\n"
        for field_type, detected in special.items():
            if detected:
                response += f"- ✓ {field_type}\n"
        
        return response
    
    async def _get_summary(self) -> str:
        """Get testing summary"""
        if not self.test_results:
            return "No testing performed yet"
        
        # Calculate statistics
        total = len(self.test_results)
        by_status = {}
        
        for result in self.test_results:
            status = result.status.value
            by_status[status] = by_status.get(status, 0) + 1
        
        success_rate = (by_status.get('success', 0) / total * 100) if total > 0 else 0
        
        response = f"""# API Testing Summary

## Overall Statistics
- **Total Endpoints Tested**: {total}
- **Success Rate**: {success_rate:.1f}%

## Results by Status
"""
        
        for status, count in by_status.items():
            response += f"- **{status}**: {count} endpoints\n"
        
        response += f"\n## Resources Discovered\n"
        for rtype, resources in self.resources.items():
            response += f"- **{rtype}**: {len(resources)} items\n"
        
        # Find special endpoints
        screenshot_endpoints = [r for r in self.test_results if r.has_screenshot_data]
        if screenshot_endpoints:
            response += f"\n## Screenshot Endpoints ({len(screenshot_endpoints)})\n"
            for ep in screenshot_endpoints:
                response += f"- `{ep.method} {ep.path}`\n"
        
        return response
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if hasattr(self, 'client') and self.client:
                await self.client.close()
        except Exception as e:
            print(f"Error during cleanup: {e}", file=sys.stderr)
    
    async def run(self):
        """Run the MCP server"""
        try:
            async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
                await self.server.run(
                    read_stream,
                    write_stream,
                    InitializationOptions(
                        server_name="datto-api-tools",
                        server_version="1.0.0",
                        capabilities=self.server.get_capabilities(
                            notification_options=NotificationOptions(),
                            experimental_capabilities={}
                        ),
                    ),
                )
        finally:
            await self.cleanup()


async def main():
    """Main entry point"""
    try:
        server = SimpleDattoMCPServer()
        await server.run()
    except Exception as e:
        print(f"Failed to run server: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    try:
        import asyncio
        asyncio.run(main())
    except KeyboardInterrupt:
        sys.exit(0)
    except Exception as e:
        print(f"Server error: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        sys.exit(1)