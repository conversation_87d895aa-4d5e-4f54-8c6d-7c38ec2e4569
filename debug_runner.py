#!/usr/bin/env python3
"""
Interactive debug runner for Datto API testing
"""
import asyncio
import argparse
import json
import os
import sys
from typing import Optional, Dict, Any
from dotenv import load_dotenv

from api_client import DattoAPIClient
from resource_discovery import ResourceDiscovery
from endpoint_tester import EndpointTester
from schema_analyzer import <PERSON>hemaAnalyzer
from debug_logger import DebugLogger


class DattoDebugRunner:
    """Interactive debugging tool for Datto API"""
    
    def __init__(self, public_key: str, secret_key: str, log_file: Optional[str] = None):
        self.public_key = public_key
        self.secret_key = secret_key
        self.logger = DebugLogger("debug_runner", log_file, verbose=True)
        self.client = None
        self.resources = {}
        
    async def __aenter__(self):
        self.client = DattoAPIClient(self.public_key, self.secret_key, debug=True)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.close()
    
    async def test_connection(self):
        """Test basic connectivity"""
        self.logger.log_phase("CONNECTION TEST", "Testing API connectivity")
        
        try:
            response = await self.client.get("/v1/bcdr/device", {"_perPage": 1})
            
            if response.status_code == 200:
                self.logger.info("✓ Connection successful!")
                self.logger.info(f"  Rate limit remaining: {self.client.rate_limit_remaining}")
                return True
            else:
                self.logger.error(f"✗ Connection failed: {response.status_code}")
                self.logger.error(f"  Response: {response.text[:200]}")
                return False
                
        except Exception as e:
            self.logger.error(f"✗ Connection error: {e}")
            return False
    
    async def discover_resources(self, resource_types: Optional[list] = None):
        """Discover resources"""
        self.logger.log_phase("RESOURCE DISCOVERY", "Discovering API resources")
        
        discovery = ResourceDiscovery(self.client)
        
        if resource_types:
            # Discover specific types
            for rtype in resource_types:
                if rtype == "device":
                    self.resources['device'] = await discovery.discover_devices()
                elif rtype == "agent":
                    self.resources['agent'] = await discovery.discover_agents()
                elif rtype == "dtc_asset":
                    self.resources['dtc_asset'] = await discovery.discover_dtc_assets()
                elif rtype == "saas_domain":
                    self.resources['saas_domain'] = await discovery.discover_saas_domains()
        else:
            # Discover all
            self.resources = await discovery.discover_all()
        
        # Print summary
        self.logger.info("\nDiscovered resources:")
        for rtype, resources in self.resources.items():
            if resources:
                self.logger.info(f"  {rtype}: {len(resources)} items")
                # Show first item as example
                if resources:
                    example = resources[0]
                    self.logger.debug(f"    Example: {example.resource_id} - {example.name}")
    
    async def test_endpoint(self, method: str, path: str, 
                          params: Optional[Dict] = None, body: Optional[Dict] = None):
        """Test a specific endpoint"""
        self.logger.log_phase("ENDPOINT TEST", f"Testing {method} {path}")
        
        tester = EndpointTester(self.client)
        
        # Parse path parameters from the path
        import re
        path_params = {}
        param_matches = re.findall(r'\{(\w+)\}', path)
        
        if param_matches and not params:
            # Try to auto-fill from discovered resources
            self.logger.info("Auto-filling parameters from discovered resources...")
            
            for param in param_matches:
                # Map parameter to resource type
                if param in ['serialNumber', 'deviceSerial'] and 'device' in self.resources:
                    if self.resources['device']:
                        path_params[param] = self.resources['device'][0].resource_id
                        self.logger.info(f"  {param} = {path_params[param]}")
                elif param in ['assetId', 'assetUuid'] and 'asset' in self.resources:
                    if self.resources['asset']:
                        path_params[param] = self.resources['asset'][0].resource_id
                        self.logger.info(f"  {param} = {path_params[param]}")
        
        # Test the endpoint
        result = await tester.test_endpoint(
            method=method,
            path=path,
            path_params=path_params or params,
            body=body
        )
        
        # Print results
        self.logger.info(f"\nResult: {result.status.value}")
        if result.status_code:
            self.logger.info(f"Status Code: {result.status_code}")
        if result.response_time_ms:
            self.logger.info(f"Response Time: {result.response_time_ms:.0f}ms")
        
        if result.status.value == "success":
            self.logger.info(f"Fields Discovered: {len(result.discovered_fields)}")
            
            if result.has_screenshot_data:
                self.logger.info("✓ Screenshot data detected!")
            if result.has_verification_data:
                self.logger.info("✓ Verification data detected!")
            
            # Show sample response
            if result.response_data:
                self.logger.info("\nSample Response:")
                if isinstance(result.response_data, dict):
                    # Show first few fields
                    sample = {k: v for i, (k, v) in enumerate(result.response_data.items()) if i < 5}
                    self.logger.info(json.dumps(sample, indent=2))
                    if len(result.response_data) > 5:
                        self.logger.info(f"... and {len(result.response_data) - 5} more fields")
                else:
                    self.logger.info(str(result.response_data)[:200] + "...")
        else:
            self.logger.error(f"Error: {result.error_message}")
        
        return result
    
    async def analyze_response(self, data: Any):
        """Analyze a response data structure"""
        self.logger.log_phase("SCHEMA ANALYSIS", "Analyzing response structure")
        
        analyzer = SchemaAnalyzer()
        
        # Extract schema
        schema = analyzer.extract_schema(data)
        self.logger.info("Extracted Schema:")
        self.logger.info(json.dumps(schema, indent=2))
        
        # Get fields
        fields = analyzer.get_all_fields(data)
        self.logger.info(f"\nFound {len(fields)} fields")
        
        # Detect special fields
        special = analyzer.detect_special_fields(data)
        self.logger.info("\nSpecial fields:")
        for field_type, detected in special.items():
            if detected:
                self.logger.info(f"  ✓ {field_type}")
    
    async def interactive_mode(self):
        """Run in interactive mode"""
        self.logger.info("\n" + "="*80)
        self.logger.info("DATTO API INTERACTIVE DEBUG MODE")
        self.logger.info("="*80)
        self.logger.info("\nCommands:")
        self.logger.info("  test <method> <path>  - Test an endpoint")
        self.logger.info("  discover [type]       - Discover resources")
        self.logger.info("  analyze               - Analyze last response")
        self.logger.info("  resources             - Show discovered resources")
        self.logger.info("  quit                  - Exit")
        
        last_result = None
        
        while True:
            try:
                cmd = input("\n> ").strip().split()
                if not cmd:
                    continue
                
                if cmd[0] == "quit":
                    break
                
                elif cmd[0] == "test":
                    if len(cmd) < 3:
                        print("Usage: test <method> <path>")
                        continue
                    
                    method = cmd[1].upper()
                    path = cmd[2]
                    last_result = await self.test_endpoint(method, path)
                
                elif cmd[0] == "discover":
                    resource_type = cmd[1] if len(cmd) > 1 else None
                    types = [resource_type] if resource_type else None
                    await self.discover_resources(types)
                
                elif cmd[0] == "analyze":
                    if last_result and last_result.response_data:
                        await self.analyze_response(last_result.response_data)
                    else:
                        print("No response data to analyze")
                
                elif cmd[0] == "resources":
                    if self.resources:
                        for rtype, resources in self.resources.items():
                            print(f"\n{rtype}: {len(resources)} items")
                            for r in resources[:3]:
                                print(f"  - {r.resource_id}: {r.name}")
                    else:
                        print("No resources discovered yet")
                
                else:
                    print(f"Unknown command: {cmd[0]}")
                    
            except KeyboardInterrupt:
                print("\nUse 'quit' to exit")
            except Exception as e:
                self.logger.error(f"Error: {e}")


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Datto API Debug Runner")
    parser.add_argument("--public-key", help="API public key (or use .env)")
    parser.add_argument("--secret-key", help="API secret key (or use .env)")
    parser.add_argument("--log-file", help="Log file path", default="debug_runner.log")
    parser.add_argument("--test", help="Test specific endpoint", nargs=2, metavar=("METHOD", "PATH"))
    parser.add_argument("--discover", help="Discover resources", action="store_true")
    parser.add_argument("--interactive", help="Interactive mode", action="store_true")
    
    args = parser.parse_args()
    
    # Load credentials
    load_dotenv()
    public_key = args.public_key or os.getenv('DATTO_PUBLIC_KEY')
    secret_key = args.secret_key or os.getenv('DATTO_SECRET_KEY')
    
    if not public_key or not secret_key:
        print("Error: API credentials required")
        print("Set in .env file or use --public-key and --secret-key")
        return 1
    
    async with DattoDebugRunner(public_key, secret_key, args.log_file) as runner:
        # Test connection first
        if not await runner.test_connection():
            return 1
        
        if args.test:
            # Test specific endpoint
            method, path = args.test
            await runner.test_endpoint(method.upper(), path)
        
        elif args.discover:
            # Discover resources
            await runner.discover_resources()
        
        elif args.interactive:
            # Interactive mode
            await runner.interactive_mode()
        
        else:
            # Default: discover then interactive
            await runner.discover_resources()
            await runner.interactive_mode()
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)