#!/usr/bin/env python3
"""
Data models for Datto API MCP Server
"""
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, List, Optional, Set


class EndpointStatus(Enum):
    """Status of endpoint testing"""
    UNTESTED = "untested"
    SUCCESS = "success"
    AUTH_FAILED = "auth_failed"
    NOT_FOUND = "not_found"
    ERROR = "error"
    RATE_LIMITED = "rate_limited"
    PARTIAL = "partial"


@dataclass
class EndpointResult:
    """Result of testing an endpoint"""
    path: str
    method: str
    status: EndpointStatus
    status_code: Optional[int] = None
    response_data: Optional[Any] = None
    error_message: Optional[str] = None
    working_params: Dict[str, Any] = field(default_factory=dict)
    failed_params: Dict[str, Any] = field(default_factory=dict)
    response_time_ms: Optional[float] = None
    headers: Dict[str, str] = field(default_factory=dict)
    discovered_fields: Set[str] = field(default_factory=set)
    has_screenshot_data: bool = False
    has_verification_data: bool = False
    response_schema: Optional[Dict[str, Any]] = None


@dataclass
class ApiResource:
    """Discovered API resource"""
    resource_type: str
    resource_id: str
    name: Optional[str] = None
    parent_type: Optional[str] = None
    parent_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    last_seen: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'name': self.name,
            'parent_type': self.parent_type,
            'parent_id': self.parent_id,
            'metadata': self.metadata,
            'last_seen': self.last_seen.isoformat()
        }