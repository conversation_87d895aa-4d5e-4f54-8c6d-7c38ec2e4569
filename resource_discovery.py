#!/usr/bin/env python3
"""
Resource discovery module for Datto API
"""
import sys
from typing import Dict, List, Any, Optional
from collections import defaultdict
from api_client import DattoAP<PERSON>lient
from models import ApiResource

# IMPORTANT: Redirect all print statements to stderr
_print = print
def safe_print(*args, **kwargs):
    """Override print to always go to stderr"""
    kwargs['file'] = sys.stderr
    return _print(*args, **kwargs)

print = safe_print


class ResourceDiscovery:
    """Discovers and catalogs API resources"""
    
    def __init__(self, client: DattoAPIClient):
        self.client = client
        self.resources: Dict[str, List[ApiResource]] = defaultdict(list)
        self.field_patterns: Dict[str, set] = defaultdict(set)
    
    async def discover_all(self) -> Dict[str, List[ApiResource]]:
        """Discover all available resources"""
        print("\n[DISCOVERY] Starting resource discovery...")
        
        # Discovery endpoints
        discovery_tasks = [
            ('device', self.discover_devices),
            ('agent', self.discover_agents),
            ('dtc_asset', self.discover_dtc_assets),
            ('saas_domain', self.discover_saas_domains),
        ]
        
        for resource_type, discovery_func in discovery_tasks:
            try:
                print(f"\n[DISCOVERY] Discovering {resource_type}...")
                resources = await discovery_func()
                self.resources[resource_type] = resources
                print(f"[DISCOVERY] Found {len(resources)} {resource_type}(s)")
            except Exception as e:
                print(f"[ERROR] Failed to discover {resource_type}: {e}")
        
        # Discover relationships
        await self.discover_relationships()
        
        return self.resources
    
    async def discover_devices(self) -> List[ApiResource]:
        """Discover BCDR devices"""
        devices = []
        
        response = await self.client.get("/v1/bcdr/device")
        if response.status_code == 200:
            data = response.json()
            items = data.get('items', [])
            
            for item in items:
                if serial := item.get('serialNumber'):
                    device = ApiResource(
                        resource_type='device',
                        resource_id=serial,
                        name=item.get('name'),
                        metadata=item
                    )
                    devices.append(device)
                    
                    # Track field patterns
                    self.field_patterns['device'].update(item.keys())
        
        return devices
    
    async def discover_agents(self) -> List[ApiResource]:
        """Discover BCDR agents"""
        agents = []
        
        response = await self.client.get("/v1/bcdr/agent")
        if response.status_code == 200:
            data = response.json()
            
            # Handle nested structure
            for client in data.get('clients', []):
                client_id = client.get('organizationId')
                
                # Store client as resource
                if client_id:
                    self.resources['client'].append(ApiResource(
                        resource_type='client',
                        resource_id=str(client_id),
                        name=client.get('clientName'),
                        metadata=client
                    ))
                
                # Store agents
                for agent in client.get('agents', []):
                    if agent_id := agent.get('uuid'):
                        agents.append(ApiResource(
                            resource_type='agent',
                            resource_id=agent_id,
                            name=agent.get('hostname'),
                            parent_type='client',
                            parent_id=str(client_id),
                            metadata=agent
                        ))
                        
                        self.field_patterns['agent'].update(agent.keys())
        
        return agents
    
    async def discover_dtc_assets(self) -> List[ApiResource]:
        """Discover Direct-to-Cloud assets"""
        assets = []
        
        try:
            response = await self.client.get("/v1/dtc/assets")
            if response.status_code == 200:
                data = response.json()
                items = data.get('items', []) if isinstance(data, dict) else data
                
                for item in items:
                    if asset_id := (item.get('assetUuid') or item.get('assetId')):
                        assets.append(ApiResource(
                            resource_type='dtc_asset',
                            resource_id=str(asset_id),
                            name=item.get('name') or item.get('hostname'),
                            metadata=item
                        ))
                        
                        self.field_patterns['dtc_asset'].update(item.keys())
        except Exception as e:
            print(f"[WARNING] DTC assets discovery failed: {e}")
        
        return assets
    
    async def discover_saas_domains(self) -> List[ApiResource]:
        """Discover SaaS protection domains"""
        domains = []
        
        try:
            response = await self.client.get("/v1/saas/domains")
            if response.status_code == 200:
                data = response.json()
                items = data.get('items', []) if isinstance(data, dict) else data
                
                for item in items:
                    if domain_id := item.get('saasCustomerId'):
                        domains.append(ApiResource(
                            resource_type='saas_domain',
                            resource_id=str(domain_id),
                            name=item.get('domain'),
                            metadata=item
                        ))
                        
                        self.field_patterns['saas_domain'].update(item.keys())
        except Exception as e:
            print(f"[WARNING] SaaS domains discovery failed: {e}")
        
        return domains
    
    async def discover_relationships(self):
        """Discover relationships between resources"""
        print("\n[DISCOVERY] Discovering relationships...")
        
        # Sample devices to find assets
        devices = self.resources.get('device', [])[:3]  # First 3 devices
        
        for device in devices:
            try:
                path = f"/v1/bcdr/device/{device.resource_id}/asset"
                response = await self.client.get(path)
                
                if response.status_code == 200:
                    data = response.json()
                    assets = data.get('items', []) if isinstance(data, dict) else data
                    
                    for asset in assets[:5]:  # First 5 assets
                        if asset_id := (asset.get('assetId') or asset.get('volume')):
                            self.resources['asset'].append(ApiResource(
                                resource_type='asset',
                                resource_id=str(asset_id),
                                name=asset.get('name'),
                                parent_type='device',
                                parent_id=device.resource_id,
                                metadata=asset
                            ))
                    
                    if assets:
                        device.metadata['accessible'] = True
                        print(f"[DISCOVERY] Device {device.resource_id} has {len(assets)} assets")
                        
            except Exception as e:
                print(f"[WARNING] Could not get assets for device {device.resource_id}: {e}")
    
    def get_sample_resource(self, resource_type: str) -> Optional[ApiResource]:
        """Get a sample resource of the specified type"""
        resources = self.resources.get(resource_type, [])
        
        # Prefer accessible resources
        if resource_type == 'device':
            accessible = [r for r in resources if r.metadata.get('accessible')]
            if accessible:
                return accessible[0]
        
        return resources[0] if resources else None
    
    def get_field_patterns(self) -> Dict[str, set]:
        """Get discovered field patterns for each resource type"""
        return dict(self.field_patterns)