#!/usr/bin/env python3
"""
Validate that the Datto MCP setup is correct
"""
import sys
import os
from pathlib import Path


def check_python_version():
    """Check Python version"""
    print("Checking Python version... ", end="")
    if sys.version_info < (3, 8):
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} (need 3.8+)")
        return False
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}")
    return True


def check_imports():
    """Check required imports"""
    required = [
        ("httpx", "httpx"),
        ("tenacity", "tenacity"),
        ("dotenv", "python-dotenv"),
        ("yaml", "pyyaml"),
        ("mcp", "mcp")
    ]
    
    all_good = True
    print("\nChecking required packages:")
    
    for module, package in required:
        print(f"  {package}... ", end="")
        try:
            __import__(module)
            print("✓")
        except ImportError:
            print(f"❌ (install with: pip install {package})")
            all_good = False
    
    return all_good


def check_files():
    """Check required files exist"""
    required_files = [
        "models.py",
        "api_client.py",
        "resource_discovery.py",
        "schema_analyzer.py",
        "endpoint_tester.py",
        "debug_logger.py",
        "test_standalone.py",
        "debug_runner.py",
        "mcp_server.py"
    ]
    
    print("\nChecking required files:")
    all_good = True
    
    for file in required_files:
        print(f"  {file}... ", end="")
        if Path(file).exists():
            print("✓")
        else:
            print("❌")
            all_good = False
    
    return all_good


def check_env():
    """Check environment setup"""
    print("\nChecking environment:")
    
    # Check .env file
    print("  .env file... ", end="")
    if Path(".env").exists():
        print("✓")
        
        # Load and check credentials
        from dotenv import load_dotenv
        load_dotenv()
        
        print("  DATTO_PUBLIC_KEY... ", end="")
        if os.getenv('DATTO_PUBLIC_KEY'):
            print("✓")
        else:
            print("❌ (not set in .env)")
            return False
        
        print("  DATTO_SECRET_KEY... ", end="")
        if os.getenv('DATTO_SECRET_KEY'):
            print("✓")
        else:
            print("❌ (not set in .env)")
            return False
    else:
        print("❌ (create from .env.example)")
        return False
    
    return True


def test_import():
    """Try importing our modules"""
    print("\nTesting module imports:")
    
    try:
        print("  Importing models... ", end="")
        from models import ApiResource, EndpointResult
        print("✓")
        
        print("  Importing api_client... ", end="")
        from api_client import DattoAPIClient
        print("✓")
        
        print("  Importing resource_discovery... ", end="")
        from resource_discovery import ResourceDiscovery
        print("✓")
        
        return True
    except Exception as e:
        print(f"❌\n  Error: {e}")
        return False


def main():
    """Run all checks"""
    print("="*50)
    print("Datto MCP Setup Validation")
    print("="*50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Required Packages", check_imports),
        ("Required Files", check_files),
        ("Environment Setup", check_env),
        ("Module Imports", test_import)
    ]
    
    all_passed = True
    
    for name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ Error in {name}: {e}")
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("✅ All checks passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run: python test_standalone.py")
        print("2. Run: python debug_runner.py")
        print("3. Configure Claude Desktop with mcp_server.py")
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        print("\nHint: Run setup_windows.bat or setup_unix.sh first")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())