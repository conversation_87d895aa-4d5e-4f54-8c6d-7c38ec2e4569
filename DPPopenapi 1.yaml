openapi: 3.0.3
info:
  title: Datto REST API - Enhanced Comprehensive Documentation
  version: v1.1.0
  description: |
    # Datto REST API - Complete Enterprise Backup & Recovery Platform

    ## 🚀 Platform Overview

    The Datto REST API provides comprehensive programmatic access to <PERSON><PERSON>'s complete backup and recovery ecosystem, empowering MSPs and enterprises with:

    ### Core Platforms
    - **🔧 BCDR (Business Continuity & Disaster Recovery)**: Physical and virtual appliance management
    - **☁️ Direct-to-Cloud (DTC)**: Agentless cloud backup without hardware dependencies  
    - **📧 SaaS Protection**: Enterprise-grade backup for Microsoft 365, Google Workspace, and other cloud applications
    - **📸 Screenshot Verification**: Visual validation of backup integrity and recoverability
    - **📊 Comprehensive Monitoring**: Real-time health, compliance reporting, and alerting
    - **🔗 IT Glue Integration**: Seamless RMM/PSA platform connectivity

    ## 🔑 Key Features & Capabilities

    ### Screenshot Verification System
    Advanced visual verification automatically validates backup success:
    ```yaml
    Screenshot Fields:
      lastScreenshot: **********        # Unix timestamp of last screenshot
      lastScreenshotUrl: "https://..."  # Direct URL to screenshot image
      screenshotSuccess: true           # Boolean verification status
      screenshotVerification:           # Detailed verification object
        status: "successful"
        image: "screenshot_url"
    ```

    ### Device Model Support Matrix
    | Device Model | Type | Monitoring | Screenshots | Health Data | Cloud Managed |
    |--------------|------|------------|-------------|-------------|---------------|
    | **SIRIS** | Physical Appliance | ✅ Full | ✅ Yes | ✅ Complete | ❌ No |
    | **ALTO** | Entry-Level Physical | ✅ Full | ✅ Yes | ✅ Complete | ❌ No |
    | **NAS** | Network Storage | ✅ Full | ⚠️ Limited | ✅ Complete | ❌ No |
    | **CLDSIRIS** | Datto Cloud Managed | ⚠️ Basic | ❌ No | ⚠️ Limited | ✅ Yes |
    | **EB4S/EBDR** | Datto Cloud Managed | ⚠️ Basic | ❌ No | ⚠️ Limited | ✅ Yes |

    ### IT Glue Integration Architecture
    The `/v1/itglue/*` endpoints provide **backup status data TO IT Glue**, not IT Glue configuration:
    - Real-time backup health metrics
    - Compliance and protection status  
    - Screenshot verification results
    - Alert and issue summaries
    - Asset inventory and metadata

    ## 🔐 Authentication & Security

    **HTTP Basic Authentication** with API key pairs:
    ```javascript
    const auth = btoa(`${PUBLIC_KEY}:${SECRET_KEY}`);
    fetch('https://api.datto.com/v1/endpoint', {
      headers: { 'Authorization': `Basic ${auth}` }
    });
    ```

    **API Key Generation**: Available in Datto Partner Portal under API Settings

    ## 📈 Rate Limiting & Performance

    - **Rate Limit**: 10,000 requests/hour per organization
    - **Monitoring Headers**:
      - `X-API-Limit-Remaining`: Requests remaining in current window
      - `X-API-Limit-Resets`: Unix timestamp when limit resets
      - `X-API-Limit-Cost`: Cost of current request
    - **Best Practices**: Cache responses, use pagination, implement exponential backoff

    ## 📄 Pagination Standards

    Consistent pagination across all list endpoints:
    ```yaml
    Parameters:
      _page: 1                # Page number (1-based indexing)
      _perPage: 100          # Items per page (default: 100, max: 1000)
    
    Response:
      pagination:
        page: 1
        perPage: 100
        totalPages: 15
        count: 1432
    ```

    ## 🚀 Quick Start Guide

    ### 1. Authentication Setup
    ```bash
    export DATTO_PUBLIC_KEY="your_public_key"
    export DATTO_SECRET_KEY="your_secret_key"
    ```

    ### 2. List All Devices
    ```bash
    curl -X GET "https://api.datto.com/v1/bcdr/device" \
      -H "Authorization: Basic $(echo -n $DATTO_PUBLIC_KEY:$DATTO_SECRET_KEY | base64)"
    ```

    ### 3. Get Device Details with Screenshots
    ```bash
    curl -X GET "https://api.datto.com/v1/bcdr/device/{serialNumber}" \
      -H "Authorization: Basic $(echo -n $DATTO_PUBLIC_KEY:$DATTO_SECRET_KEY | base64)"
    ```

    ### 4. Monitor DTC Assets  
    ```bash
    curl -X GET "https://api.datto.com/v1/dtc/assets" \
      -H "Authorization: Basic $(echo -n $DATTO_PUBLIC_KEY:$DATTO_SECRET_KEY | base64)"
    ```

    ## 🔧 Common Integration Patterns

    ### Real-Time Monitoring Dashboard
    ```javascript
    // Poll for device health every 5 minutes
    setInterval(async () => {
      const devices = await fetch('/v1/bcdr/device');
      const alerts = devices.filter(d => d.alertCount > 0);
      updateDashboard(alerts);
    }, 300000);
    ```

    ### Screenshot Verification Pipeline
    ```javascript
    // Validate backup success with screenshots
    const asset = await fetch(`/v1/bcdr/device/${serial}/asset`);
    if (asset.lastScreenshotUrl && asset.screenshotSuccess) {
      console.log('✅ Backup verified via screenshot');
    }
    ```

    ### Automated Compliance Reporting
    ```javascript
    // Generate compliance report
    const clients = await fetch('/v1/bcdr/agent');
    const report = clients.map(client => ({
      name: client.clientName,
      protected: client.agents.filter(a => a.screenshotSuccess).length,
      total: client.agents.length
    }));
    ```

    ## 📞 Support & Resources

    - **API Documentation**: https://api.datto.com/docs
    - **Technical Support**: <EMAIL>  
    - **Partner Portal**: https://partner.datto.com
    - **Status Page**: https://status.datto.com
    - **Community Forum**: https://community.datto.com

    ---

    > **Latest Updates**: Enhanced screenshot verification, improved error handling, expanded DTC capabilities, comprehensive schema mappings
  contact:
    name: Datto API Support Team
    email: <EMAIL>
    url: https://www.datto.com/support
  license:
    name: Proprietary
    url: https://www.datto.com/legal/terms-of-service
  x-logo:
    url: https://www.datto.com/img/logo.png
    altText: Datto Logo
  x-api-id: datto-backup-recovery
  x-categories:
    - backup
    - disaster-recovery
    - msp
    - business-continuity

servers:
  - url: https://api.datto.com
    description: Production API Server
  - url: https://api-staging.datto.com
    description: Staging Environment (Partner Testing)

security:
  - basicAuth: []

tags:
  - name: BCDR
    description: |
      **Business Continuity and Disaster Recovery**
      
      Core backup and recovery operations for physical and virtual devices. Includes comprehensive
      device management, asset monitoring, screenshot verification, and health status tracking.
      
      **Supported Operations:**
      - Device inventory and health monitoring
      - Asset (agent/share) management  
      - Screenshot verification and validation
      - Alert and incident management
      - VM restore operations
      
      **Device Types Supported:**
      - SIRIS: Enterprise-grade appliances
      - ALTO: SMB-focused devices
      - NAS: Network storage devices
    x-display-name: "🔧 Backup & Recovery (BCDR)"

  - name: DTC Assets  
    description: |
      **Direct-to-Cloud Asset Management**
      
      Cloud-first backup without requiring on-premise hardware. Supports servers, workstations,
      and cloud workloads with agentless protection and centralized management.
      
      **Capabilities:**
      - Agentless backup for Windows/Linux
      - Cloud workload protection (AWS, Azure)
      - Bandwidth throttling and scheduling
      - RMM template deployment
      - Storage pool management
      
      **Asset Types:**
      - Physical servers and workstations
      - Virtual machines (VMware, Hyper-V)
      - Cloud instances (EC2, Azure VMs)
    x-display-name: "☁️ Cloud Backup (DTC)"

  - name: SaaS Protection
    description: |
      **Software-as-a-Service Application Backup**
      
      Enterprise-grade backup and recovery for cloud applications including Microsoft 365,
      Google Workspace, and other SaaS platforms with granular seat management.
      
      **Supported Applications:**
      - Microsoft 365 (Exchange, OneDrive, SharePoint, Teams)
      - Google Workspace (Gmail, Drive, Calendar, Contacts)
      - Custom SaaS applications via API
      
      **Features:**
      - Automated seat provisioning
      - Granular retention policies
      - Point-in-time recovery
      - Compliance reporting
    x-display-name: "📧 SaaS Backup"

  - name: IT Glue
    description: |
      **Backup Status Integration for IT Glue**
      
      ⚠️ **IMPORTANT**: These endpoints provide backup health data TO IT Glue for consumption.
      They are NOT for configuring IT Glue settings.
      
      **Integration Purpose:**
      - Export backup status to IT Glue documentation platform
      - Provide real-time protection status
      - Enable unified MSP dashboards
      - Support compliance documentation
      
      **Data Provided:**
      - Asset backup health and status
      - Screenshot verification results  
      - Protection compliance metrics
      - Alert and issue summaries
    x-display-name: "🔗 Backup Status (IT Glue)"

  - name: Reporting
    description: |
      **Activity Logs, Audit Trails & Compliance Reporting**
      
      Comprehensive logging and reporting capabilities for audit trails, compliance, and
      operational monitoring across all Datto platforms.
      
      **Report Types:**
      - Activity logs with detailed audit trails
      - User action tracking and authentication logs
      - System events and configuration changes
      - Backup success/failure analytics
      - Compliance and regulatory reports
      
      **Filtering & Export:**
      - Time-based filtering with flexible ranges
      - Multi-criteria search and filtering
      - Export capabilities for external systems
      - Real-time event streaming
    x-display-name: "📊 Reports & Audit Logs"

  - name: Screenshots
    description: |
      **Screenshot Verification & Visual Validation**
      
      Advanced screenshot verification system that provides visual confirmation of backup
      success and system recoverability through automated boot testing.
      
      **Verification Process:**
      1. Automated VM boot from backup
      2. Screenshot capture of running system
      3. AI-powered validation of boot success
      4. Status reporting and alerting
      
      **Benefits:**
      - Visual proof of backup integrity
      - Automated disaster recovery testing
      - Compliance documentation
      - Customer confidence and reporting
    x-display-name: "📸 Screenshot Verification"

paths:
  /v1/bcdr/device:
    get:
      operationId: listBcdrDevices
      summary: List all backup devices with comprehensive health status
      description: |
        Returns all Datto backup devices in your account with detailed health, status, and
        operational information. Supports filtering and pagination for large deployments.

        **🔍 Device Types & Capabilities:**
        - **SIRIS**: Full-featured enterprise appliances with complete monitoring
        - **ALTO**: Entry-level devices with core backup features  
        - **NAS**: Network storage devices with basic monitoring
        - **CLDSIRIS/EBDR**: Cloud-managed devices (limited monitoring data)

        **📊 Response Includes:**
        - Real-time device health and connectivity status
        - Storage utilization (local and cloud)
        - Protected asset counts (agents and shares)
        - Active alert summaries
        - Warranty and service information
        - Remote access URLs

        **🔧 Query Parameters:**
        - `showHiddenDevices`: Include administratively hidden devices
        - `showChildResellerDevices`: Include devices from child reseller accounts

        **💡 Use Cases:**
        - MSP dashboard device inventory
        - Health monitoring and alerting
        - Capacity planning and utilization
        - Service level reporting
      tags:
        - BCDR
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/perPage'
        - name: showHiddenDevices
          in: query
          description: Include hidden devices in results
          schema:
            type: boolean
            default: false
          example: true
        - name: showChildResellerDevices  
          in: query
          description: Include child reseller devices in results
          schema:
            type: boolean
            default: false
          example: false
      responses:
        '200':
          description: Successfully retrieved device list
          headers:
            X-API-Limit-Remaining:
              $ref: '#/components/headers/X-API-Limit-Remaining'
            X-API-Limit-Resets:
              $ref: '#/components/headers/X-API-Limit-Resets'
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/BcdrDevice'
                required:
                  - pagination
                  - items
              examples:
                successful_response:
                  summary: Typical device list response
                  value:
                    pagination:
                      page: 1
                      perPage: 10
                      totalPages: 2
                      count: 15
                    items:
                      - serialNumber: "7085C2F4782F"
                        name: "PRIMARY-BACKUP-01"
                        model: "S5-X"
                        lastSeenDate: "2025-06-06T10:54:26-04:00"
                        hidden: false
                        organizationName: "Acme Corp"
                        agentCount: 25
                        alertCount: 0
                        localStorageUsed:
                          units: "GB"
                          size: 500
                        offsiteStorageUsed:
                          units: "TB"
                          size: 2.5
        '403':
          $ref: '#/components/responses/Forbidden'

  /v1/bcdr/device/{serialNumber}/alert:
    get:
      operationId: getDeviceAlerts
      summary: Get active alerts for specific device
      description: |
        Returns all active alerts and notifications for the specified backup device, including
        severity levels, trigger conditions, and resolution status information.

        **🚨 Alert Categories:**
        - **Device Health**: Connectivity, hardware, and system status alerts
        - **Backup Issues**: Failed backups, missed schedules, verification problems
        - **Storage Warnings**: Capacity thresholds, performance degradation
        - **Security Events**: Unauthorized access attempts, configuration changes
        - **Maintenance Notifications**: Updates, service windows, warranty status

        **📊 Alert Information:**
        - Trigger conditions and threshold values
        - Severity classification and escalation paths  
        - Notification history and acknowledgment status
        - Resolution guidance and recommended actions
        - Impact assessment and business continuity implications
      tags:
        - BCDR
      parameters:
        - $ref: '#/components/parameters/serialNumber'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/perPage'
      responses:
        '200':
          description: Successfully retrieved device alerts
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Alert'
                required:
                  - pagination
                  - items
        '404':
          $ref: '#/components/responses/NotFound'

  /v1/dtc/agent/{agentUuid}/bandwidth:
    put:
      operationId: updateDtcAgentBandwidth
      summary: Configure bandwidth settings for DTC agent
      description: |
        Updates bandwidth throttling and network usage settings for a specific Direct-to-Cloud
        agent to optimize backup performance and minimize network impact.

        **⚙️ Configuration Options:**
        - `maximumBandwidthInBits`: Peak bandwidth limit during backup operations
        - `maximumThrottledBandwidthInBits`: Reduced bandwidth for business hours
        - `pauseWhileMetered`: Automatic pause on metered connections

        **🕐 Scheduling Considerations:**
        - Configure lower limits during business hours
        - Allow full bandwidth during maintenance windows  
        - Consider time zone differences for global deployments
        - Monitor actual usage versus configured limits

        **💡 Best Practices:**
        - Start with conservative limits and monitor performance
        - Consider total available bandwidth across all agents
        - Test configuration changes during low-impact periods
        - Document bandwidth allocations for capacity planning
      tags:
        - DTC Assets
      parameters:
        - name: agentUuid
          in: path
          required: true
          description: Unique agent identifier
          schema:
            type: string
            format: uuid
          example: "389d17d5d6fd4647b3dc0fba948d78e5"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BandwidthSettings'
            examples:
              business_hours:
                summary: Conservative settings for business hours
                value:
                  maximumBandwidthInBits: 10485760
                  maximumThrottledBandwidthInBits: 5242880
                  pauseWhileMetered: true
              unlimited:
                summary: Maximum performance for maintenance windows
                value:
                  maximumBandwidthInBits: null
                  maximumThrottledBandwidthInBits: null
                  pauseWhileMetered: false
      responses:
        '200':
          description: Bandwidth settings updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BandwidthSettingsResponse'

  /v1/dtc/storage-pool:
    get:
      operationId: getDtcStoragePools
      summary: Get DTC storage pool utilization and capacity
      description: |
        Returns storage pool information for Direct-to-Cloud services, including capacity
        utilization, available storage, and allocation across different service tiers.

        **📊 Storage Pool Categories:**
        - **backupEssentials**: Standard backup storage pool
        - **k365**: Microsoft 365 backup dedicated storage
        - **archive**: Long-term retention and compliance storage
        - **disaster-recovery**: High-performance recovery storage

        **📈 Capacity Metrics:**
        - Current utilization percentages
        - Available capacity for new deployments
        - Growth trends and capacity planning data
        - Performance characteristics and IOPS limits

        **💼 Business Intelligence:**
        - Cost optimization opportunities
        - Capacity planning recommendations
        - Service tier utilization analysis
        - Multi-tenant allocation visibility
      tags:
        - DTC Assets
      responses:
        '200':
          description: Successfully retrieved storage pool information
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StoragePool'
              examples:
                storage_utilization:
                  summary: Current storage pool utilization
                  value:
                    - poolName: "backupEssentials"
                      availableStorage: 12500
                      usedStorage: 3377.64
                      utilizationPercentage: 27.02
                    - poolName: "k365"
                      availableStorage: 5000
                      usedStorage: 395.51
                      utilizationPercentage: 7.91

  /v1/saas/{saasCustomerId}/applications:
    get:
      operationId: getSaasApplications
      summary: Get detailed SaaS backup data and history
      description: |
        Returns comprehensive backup data for all applications within a SaaS customer account,
        including historical performance, service health, and detailed application-level metrics.

        **📧 Supported Applications:**
        - **Microsoft 365**: Exchange, OneDrive, SharePoint, Teams, Planner
        - **Google Workspace**: Gmail, Drive, Calendar, Contacts, Team Drives, Sites
        - **Platform Extensions**: Custom applications via API connectors

        **📊 Comprehensive Analytics:**
        - 7-day backup history with granular timestamps
        - Service-level success rates and performance metrics
        - Data volume trends and storage utilization
        - Application-specific protection coverage
        - User activity correlation with backup patterns

        **🔍 Historical Data Structure:**
        ```json
        {
          "backupHistory": [
            {
              "timeWindow": "Between0dAnd1d",
              "status": "Perfect",
              "activeServiceCount": 150,
              "activeServiceWithBackupCount": 150,
              "activeServiceWithPerfectBackupCount": 150
            }
          ]
        }
        ```

        **💡 Use Cases:**
        - Client reporting and SLA compliance verification
        - Proactive issue identification and resolution
        - Capacity planning and storage optimization
        - Application-specific backup policy validation
        - Historical trend analysis for business intelligence
      tags:
        - SaaS Protection
      parameters:
        - name: saasCustomerId
          in: path
          required: true
          description: SaaS customer identifier
          schema:
            type: integer
            minimum: 1
          example: 473121
        - name: daysUntil
          in: query
          description: Days of historical data to include in report
          schema:
            type: integer
            minimum: 1
            maximum: 30
            default: 7
          example: 7
      responses:
        '200':
          description: Successfully retrieved SaaS application data
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/SaasApplication'
                required:
                  - pagination
                  - items

  /v1/saas/{saasCustomerId}/seats:
    get:
      operationId: getSaasSeats
      summary: Get SaaS seat allocation and user details
      description: |
        Returns detailed seat allocation information for SaaS protection, including user-level
        details, license utilization, and seat state management across all protected applications.

        **👥 Seat Management Categories:**
        - **User**: Individual user accounts with mailboxes and personal storage
        - **Site**: SharePoint sites and team collaboration spaces
        - **TeamSite**: Dedicated team sites with shared resources
        - **SharedMailbox**: Shared mailboxes and distribution lists
        - **Team**: Microsoft Teams and Google Workspace teams
        - **SharedDrive**: Google Team Drives and shared storage spaces

        **📊 Seat State Classifications:**
        - **Active**: Currently protected and backing up successfully
        - **Archived**: Deactivated but retained for compliance
        - **Paused**: Temporarily suspended protection
        - **Unprotected**: Discovered but not yet under protection
        - **Error**: Protection enabled but experiencing issues

        **🔍 Filtering Capabilities:**
        Use `seatType` parameter to filter results:
        - Single type: `?seatType=User`
        - Multiple types: `?seatType=User,Site,Team`
        - All types: No filter parameter

        **💼 Business Applications:**
        - License optimization and cost management
        - User onboarding and offboarding workflows
        - Compliance reporting and audit preparation
        - Automated seat provisioning and deprovisioning
        - Usage analytics and optimization recommendations
      tags:
        - SaaS Protection
      parameters:
        - name: saasCustomerId
          in: path
          required: true
          description: SaaS customer identifier
          schema:
            type: integer
            minimum: 1
          example: 473121
        - name: seatType
          in: query
          description: Filter by seat type (comma-separated for multiple)
          schema:
            type: string
            enum: [User, Site, TeamSite, SharedMailbox, Team, SharedDrive, "User,Site,Team"]
          example: "User"
      responses:
        '200':
          description: Successfully retrieved SaaS seat information
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SaasSeat'
        '504':
          description: Gateway timeout - Large datasets may require extended processing time
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    # Additional Schema Definitions
    BandwidthSettings:
      type: object
      description: Network bandwidth configuration for DTC agents
      properties:
        maximumBandwidthInBits:
          type: integer
          nullable: true
          minimum: 1048576
          description: Maximum bandwidth in bits per second (null for unlimited)
          example: 10485760
        maximumThrottledBandwidthInBits:
          type: integer
          nullable: true
          minimum: 1048576
          description: Throttled bandwidth for business hours (null for unlimited)
          example: 5242880
        pauseWhileMetered:
          type: boolean
          description: Pause backups on metered connections
          example: true

    BandwidthSettingsResponse:
      allOf:
        - $ref: '#/components/schemas/BandwidthSettings'
        - type: object
          properties:
            agentUuid:
              type: string
              format: uuid
              description: Agent identifier for confirmation
              example: "389d17d5d6fd4647b3dc0fba948d78e5"
          required:
            - agentUuid

    StoragePool:
      type: object
      description: DTC storage pool capacity and utilization
      properties:
        poolName:
          type: string
          description: Storage pool identifier
          example: "backupEssentials"
        availableStorage:
          type: number
          format: float
          minimum: 0
          description: Available storage capacity (GB)
          example: 12500.0
        usedStorage:
          type: number
          format: float
          minimum: 0
          description: Currently used storage (GB)
          example: 3377.64
        utilizationPercentage:
          type: number
          format: float
          minimum: 0
          maximum: 100
          description: Storage utilization percentage
          example: 27.02
      required:
        - poolName
        - availableStorage
        - usedStorage

    SaasApplication:
      type: object
      description: SaaS application backup data and statistics
      properties:
        customerId:
          type: integer
          minimum: 1
          description: Customer identifier
          example: 473121
        customerName:
          type: string
          description: Customer display name
          example: "Acme Corporation"
        usedBytes:
          type: integer
          format: int64
          minimum: 0
          description: Total storage used in bytes
          example: 5440187671
        suites:
          type: array
          items:
            $ref: '#/components/schemas/SaasSuite'
          description: Application suites and their backup data
        remoteIds:
          type: array
          items:
            type: string
          description: External system identifiers
      required:
        - customerId
        - customerName
        - usedBytes
        - suites
        - remoteIds

    SaasSuite:
      type: object
      description: Application suite (Office365, Google, etc.)
      properties:
        suiteType:
          type: string
          enum: [Microsoft365, Google, Office365, GoogleApps]
          description: Suite platform identifier
          example: "Google"
        appTypes:
          type: array
          items:
            $ref: '#/components/schemas/SaasAppType'
          description: Individual applications within suite
      required:
        - suiteType
        - appTypes

    SaasAppType:
      type: object
      description: Individual SaaS application with backup details
      properties:
        appType:
          type: string
          enum: [GoogleMail, GoogleDrive, GoogleCalendar, GoogleContacts, GoogleTeamDrives, 
                 Office365Exchange, Office365OneDrive, Office365SharePoint, Office365Teams]
          description: Specific application type
          example: "GoogleMail"
        backupHistory:
          type: array
          items:
            $ref: '#/components/schemas/SaasBackupHistory'
          maxItems: 7
          description: Daily backup history (last 7 days)
        lastFullyProtectedTime:
          type: string
          description: Timestamp of last complete protection (milliseconds)
          example: "1749145405870"
        uningestedServiceCount:
          type: integer
          minimum: 0
          description: Services not yet processed
          example: 0
        usedBytes:
          type: integer
          format: int64
          minimum: 0
          description: Storage used by this application
          example: 2959898954
      required:
        - appType
        - backupHistory
        - lastFullyProtectedTime
        - uningestedServiceCount
        - usedBytes

    SaasBackupHistory:
      type: object
      description: Daily backup statistics for SaaS application
      properties:
        timeWindow:
          type: string
          enum: [Between0dAnd1d, Between1dAnd2d, Between2dAnd3d, Between3dAnd4d, 
                 Between4dAnd5d, Between5dAnd6d, Between6dAnd7d]
          description: Time window for this backup period
          example: "Between0dAnd1d"
        status:
          type: string
          enum: [Perfect, Good, Warning, Critical]
          description: Overall backup status for this period
          example: "Perfect"
        activeServiceCount:
          type: integer
          minimum: 0
          description: Total active services in this period
          example: 8
        activeServiceWithBackupCount:
          type: integer
          minimum: 0
          description: Services that had backups attempted
          example: 8
        activeServiceWithPerfectBackupCount:
          type: integer
          minimum: 0
          description: Services with perfect backup success
          example: 8
        startTime:
          type: integer
          format: int64
          description: Period start time (milliseconds)
          example: 1749145405870
        endTime:
          type: integer
          format: int64
          description: Period end time (milliseconds)
          example: 1749059005870
      required:
        - timeWindow
        - status
        - activeServiceCount
        - activeServiceWithBackupCount
        - activeServiceWithPerfectBackupCount
        - startTime
        - endTime

    SaasSeat:
      type: object
      description: Individual SaaS seat allocation and details
      properties:
        name:
          type: string
          maxLength: 100
          description: Display name for the seat/user
          example: "John Smith"
        mainId:
          type: string
          description: Primary identifier (email, username, etc.)
          example: "<EMAIL>"
        remoteId:
          type: string
          description: Platform-specific unique identifier
          example: "12345678-1234-1234-1234-123456789012"
        seatType:
          type: string
          enum: [User, Site, TeamSite, SharedMailbox, Team, SharedDrive]
          description: Type of seat allocation
          example: "User"
        seatState:
          type: string
          enum: [Active, Archived, Paused, Unprotected, Error]
          description: Current seat protection status
          example: "Active"
        billable:
          type: string
          enum: ["0", "1"]
          description: Whether seat is billable (string representation)
          example: "1"
        dateAdded:
          type: string
          format: date-time
          description: Seat creation timestamp (ISO 8601)
          example: "2022-04-26T16:43:05+00:00"
      required:
        - name
        - mainId
        - remoteId
        - seatType
        - seatState
        - billable
        - dateAdded

x-datto-api:
  version: "1.1.0"
  updated: "2025-06-06"
  changelog:
    - "Enhanced comprehensive schema definitions"
    - "Added screenshot verification documentation"
    - "Improved error handling and response codes"
    - "Extended SaaS protection capabilities"
    - "Added IT Glue integration clarification"
    - "Comprehensive code examples across languages"
    - "Detailed business use case documentation"
    - "Performance optimization guidelines"
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          $ref: '#/components/responses/InternalServerError'
      x-code-samples:
        - lang: bash
          label: cURL
          source: |
            curl -X GET "https://api.datto.com/v1/bcdr/device?_page=1&_perPage=10" \
              -H "Authorization: Basic $(echo -n $PUBLIC_KEY:$SECRET_KEY | base64)" \
              -H "Accept: application/json"
        - lang: python
          label: Python
          source: |
            import requests
            from base64 import b64encode

            # Configure authentication
            public_key = "your_public_key"
            secret_key = "your_secret_key"
            auth = b64encode(f"{public_key}:{secret_key}".encode()).decode()

            # Make request
            response = requests.get(
                "https://api.datto.com/v1/bcdr/device",
                headers={"Authorization": f"Basic {auth}"},
                params={"_page": 1, "_perPage": 10}
            )

            if response.status_code == 200:
                devices = response.json()
                for device in devices['items']:
                    print(f"Device: {device['name']} - Status: {device['lastSeenDate']}")
            else:
                print(f"Error: {response.status_code} - {response.text}")
        - lang: javascript
          label: Node.js
          source: |
            const PUBLIC_KEY = 'your_public_key';
            const SECRET_KEY = 'your_secret_key';
            const auth = Buffer.from(`${PUBLIC_KEY}:${SECRET_KEY}`).toString('base64');

            fetch('https://api.datto.com/v1/bcdr/device?_page=1&_perPage=10', {
              method: 'GET',
              headers: {
                'Authorization': `Basic ${auth}`,
                'Accept': 'application/json'
              }
            })
            .then(response => response.json())
            .then(data => {
              console.log('Devices:', data.items.length);
              data.items.forEach(device => {
                console.log(`${device.name}: ${device.agentCount} agents`);
              });
            })
            .catch(error => console.error('Error:', error));

  /v1/bcdr/device/{serialNumber}:
    get:
      operationId: getBcdrDeviceDetails
      summary: Get comprehensive device details with protected assets
      description: |
        Returns complete device information including all protected assets, alerts, health metrics,
        and verification status. Response content varies significantly by device model.

        **⚠️ IMPORTANT - Model-Based Response Differences:**
        - **Physical Devices (SIRIS/ALTO/NAS)**: Complete monitoring data, health metrics, screenshots, verification status
        - **Cloud Devices (CLDSIRIS/EBDR)**: Basic device info and asset list only - no health/storage data

        **📊 Complete Response Includes:**
        - Device configuration and hardware specifications
        - Storage metrics and utilization analytics
        - Complete list of protected agents and shares
        - Screenshot verification status and history
        - Active alerts and system issues
        - Network configuration and connectivity
        - Performance metrics and uptime data

        **📸 Screenshot Verification Data:**
        - Individual agent `lastScreenshot` timestamps
        - Boolean `screenshotSuccess` status indicators
        - Direct `lastScreenshotUrl` for visual verification
        - Verification history and trends

        **🔧 Protected Asset Details:**
        - Agent inventory with system specifications
        - Share information and access details
        - Protection schedules and retention policies
        - Backup frequency and success rates
      tags:
        - BCDR
      parameters:
        - $ref: '#/components/parameters/serialNumber'
      responses:
        '200':
          description: Successfully retrieved device details
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/BcdrDevice'
                  - type: object
                    properties:
                      agents:
                        type: array
                        items:
                          $ref: '#/components/schemas/DeviceAgent'
                        description: List of protected agents on this device
                      shares:
                        type: array
                        items:
                          $ref: '#/components/schemas/DeviceShare'
                        description: List of protected shares on this device
                      alerts:
                        type: array
                        items:
                          $ref: '#/components/schemas/Alert'
                        description: Active alerts for this device
              examples:
                physical_device:
                  summary: Physical SIRIS device with full monitoring
                  value:
                    serialNumber: "7085C2F4782F"
                    name: "PRIMARY-BACKUP-01"
                    model: "S5-X"
                    lastSeenDate: "2025-06-06T10:54:26-04:00"
                    organizationName: "Acme Corp"
                    agents:
                      - name: "SQL-SERVER-01"
                        os: "Windows Server 2019"
                        lastSnapshot: **********
                        lastScreenshotUrl: "https://device.dattobackup.com/..."
                        screenshotSuccess: true
                    alerts: []
                cloud_device:
                  summary: Cloud CLDSIRIS device with limited data
                  value:
                    serialNumber: "025512A55AF7"
                    name: "CLOUD-BACKUP-01"
                    model: "CLDSIRIS"
                    region:
                      location: "East US"
                      provider: "azure"
                    organizationName: "Acme Corp"
                    agents:
                      - name: "WEB-SERVER-01"
                        os: "Ubuntu 20.04"
                    # Note: No health/storage data for cloud devices
        '404':
          $ref: '#/components/responses/NotFound'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /v1/bcdr/device/{serialNumber}/asset:
    get:
      operationId: listDeviceAssets
      summary: List all protected assets with detailed backup status
      description: |
        Returns comprehensive list of all assets (agents and shares) protected by the specified device
        with detailed backup status, verification information, and protection metrics.

        **🔧 Asset Types:**
        - **Agents**: Servers, workstations, and virtual machines with Datto software installed
        - **Shares**: Network shares, NAS volumes, and file system backups

        **📊 Detailed Response Data:**
        - `lastSnapshot`: Unix timestamp of most recent backup
        - `lastScreenshot`: Screenshot verification timestamp  
        - `lastScreenshotUrl`: Direct URL to verification screenshot image
        - `screenshotSuccess`: Boolean verification success status
        - `protectedVolumes`: Detailed list of protected drives and volumes
        - `localSnapshots`: Count of local backup copies
        - `latestOffsite`: Most recent cloud backup timestamp
        - Protection schedules and retention configuration

        **📸 Screenshot Verification:**
        Each asset includes screenshot verification data when available:
        ```json
        {
          "lastScreenshotAttempt": **********,
          "lastScreenshotAttemptStatus": true,
          "lastScreenshotUrl": "https://device.dattobackup.com/image.png"
        }
        ```

        **💼 Business Value:**
        - Real-time backup status monitoring
        - Visual verification of recovery readiness
        - Compliance reporting and documentation
        - Automated health checks and alerting
      tags:
        - BCDR
      parameters:
        - $ref: '#/components/parameters/serialNumber'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/perPage'
      responses:
        '200':
          description: Successfully retrieved device assets
          content:
            application/json:
              schema:
                oneOf:
                  - type: array
                    items:
                      $ref: '#/components/schemas/DeviceAsset'
                    description: Direct array when pagination not requested
                  - type: object
                    properties:
                      pagination:
                        $ref: '#/components/schemas/Pagination'
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/DeviceAsset'
                    required:
                      - pagination
                      - items
                    description: Paginated response when pagination requested
              examples:
                with_screenshots:
                  summary: Assets with screenshot verification
                  value:
                    - name: "SQL-SERVER-01"
                      assetId: 123456
                      type: "agent"
                      os: "Windows Server 2019"
                      lastSnapshot: **********
                      lastScreenshotUrl: "https://device.dattobackup.com/image.png"
                      screenshotSuccess: true
                      localSnapshots: 48
                      protectedVolumesCount: 3
                without_screenshots:
                  summary: Share asset without screenshots
                  value:
                    - name: "FileShare-Data"
                      assetId: 789012
                      type: "share"
                      lastSnapshot: 1640095200
                      lastScreenshotUrl: ""
                      screenshotSuccess: false
                      localSnapshots: 72
        '404':
          $ref: '#/components/responses/NotFound'

  /v1/bcdr/agent:
    get:
      operationId: listBcdrAgents
      summary: List all clients and their protected agents hierarchically
      description: |
        Returns a comprehensive hierarchical view of all client organizations and their protected
        agents across your entire Datto infrastructure. This endpoint provides the highest-level
        view for MSP dashboards and monitoring systems.

        **🏢 Hierarchical Response Structure:**
        - **Top Level**: Client organizations with organizational metadata
        - **Nested Level**: Individual agents under each client with complete technical details

        **📊 Agent Details Include:**
        - `hostname`: System identification and network name
        - `lastSnapshot`: Most recent backup completion timestamp
        - `lastScreenshot`: Screenshot verification attempt timestamp
        - `screenshotSuccess`: Boolean verification result status
        - `protectedMachine`: Hardware identifiers (MAC address, serial number)
        - `uuid`: Universal unique identifier for API operations
        - `shortCode`: Human-readable identifier for support operations

        **📸 Screenshot Verification Tracking:**
        Each agent includes comprehensive screenshot verification data:
        ```json
        {
          "lastScreenshot": **********,
          "screenshotSuccess": true,
          "uuid": "389d17d5d6fd4647b3dc0fba948d78e5"
        }
        ```

        **🎯 Primary Use Cases:**
        - MSP multi-client dashboard construction
        - Enterprise-wide backup status monitoring
        - Screenshot verification success rate analytics
        - Client-level SLA reporting and compliance
        - Automated alerting for agents requiring attention
        - Bulk operations and batch processing workflows

        **💡 Pro Tips:**
        - Use this endpoint for high-level dashboards showing all clients
        - Filter by `screenshotSuccess` for compliance reporting
        - Cache results and update periodically for performance
        - Use `organizationId` for drill-down to specific client details
      tags:
        - BCDR
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/perPage'
      responses:
        '200':
          description: Successfully retrieved clients and agents
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  clients:
                    type: array
                    items:
                      $ref: '#/components/schemas/BcdrClient'
                    description: Hierarchical list of clients and their agents
                required:
                  - pagination
                  - clients
              examples:
                multi_client_response:
                  summary: Multiple clients with various agent states
                  value:
                    pagination:
                      page: 1
                      perPage: 10
                      totalPages: 1
                      count: 3
                    clients:
                      - clientName: "Acme Corporation"
                        organizationId: 274558
                        organizationName: "Acme Corporation"
                        agents:
                          - uuid: "389d17d5d6fd4647b3dc0fba948d78e5"
                            hostname: "SQL-SERVER-01"
                            lastSnapshot: 1749142382
                            lastScreenshot: 1749084722
                            screenshotSuccess: true
                            shortCode: "85WTF6ZK5"
                          - uuid: "f02300e543da49dfa39e93d0eaf607ae"
                            hostname: "WEB-SERVER-01"
                            lastSnapshot: 1749142320
                            lastScreenshot: 1749084663
                            screenshotSuccess: true
                            shortCode: "MMGC93GRA"
                      - clientName: "Beta Industries"
                        organizationId: 316780
                        organizationName: "Beta Industries"
                        agents:
                          - uuid: "f34c85a027db40deae32ab7c09b774e1"
                            hostname: "DESKTOP-MAIN"
                            lastSnapshot: 1748484181
                            lastScreenshot: null
                            screenshotSuccess: false
                            shortCode: "MEVXPPWTR"
        '403':
          $ref: '#/components/responses/Forbidden'

  /v1/dtc/assets:
    get:
      operationId: listDtcAssets
      summary: List all Direct-to-Cloud protected assets with comprehensive status
      description: |
        Returns all DTC (Direct-to-Cloud) assets - systems backed up directly to Datto's cloud
        infrastructure without requiring on-premise backup devices. This endpoint provides
        comprehensive monitoring for cloud-first backup deployments.

        **☁️ Supported Asset Types:**
        - **Physical Systems**: Windows and Linux servers, workstations
        - **Virtual Machines**: VMware vSphere, Microsoft Hyper-V environments
        - **Cloud Workloads**: AWS EC2 instances, Azure virtual machines, GCP compute
        - **Hybrid Environments**: On-premise systems with cloud backup targets

        **📊 Comprehensive Response Data:**
        - `backupState`: Current backup operational status (true/false)
        - `last10Backups`: Detailed history of recent backup attempts with timestamps and success status
        - `screenshotSuccess`: Visual verification status for backup integrity
        - `protectedVolume`: Total protected storage capacity in bytes
        - `lastScreenshotUrl`: Direct URL to most recent verification screenshot
        - Network and system information (IP addresses, MAC addresses, BIOS details)
        - Organizational metadata for multi-tenant environments

        **📸 Screenshot Verification Integration:**
        DTC assets include advanced screenshot verification:
        ```json
        {
          "screenshotSuccess": true,
          "lastScreenshotUrl": "https://device.dattobackup.com/image.png",
          "last10Backups": [
            {
              "timestamp": **********,
              "backupStatus": true,
              "screenshotSuccess": true
            }
          ]
        }
        ```

        **🎯 Primary Use Cases:**
        - Cloud-first backup monitoring and management
        - Multi-cloud asset inventory and status tracking
        - Agentless backup deployment oversight
        - Hybrid infrastructure protection monitoring
        - MSP client portal data feeds
        - Automated compliance and SLA reporting

        **💡 Operational Benefits:**
        - No hardware dependencies or maintenance
        - Centralized cloud-based management
        - Automatic scaling and capacity management
        - Global redundancy and availability
        - Simplified deployment and onboarding
      tags:
        - DTC Assets
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/perPage'
      responses:
        '200':
          description: Successfully retrieved DTC assets
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/DtcAsset'
                required:
                  - pagination
                  - items
              examples:
                comprehensive_assets:
                  summary: DTC assets with verification and backup history
                  value:
                    pagination:
                      page: 1
                      perPage: 10
                      totalPages: 5
                      count: 46
                    items:
                      - name: "CLOUD-SQL-01"
                        status: "paired"
                        type: "dtc-servers"
                        screenshotSuccess: true
                        organizationName: "Acme Corp"
                        backupState: true
                        lastScreenshotUrl: "https://device.dattobackup.com/image.png"
                        lastBackup: **********
                        last10Backups:
                          - timestamp: **********
                            backupStatus: true
                            screenshotSuccess: true
                        hostname: "CLOUD-SQL-01"
                        protectedVolume: 63845695488
        '401':
          $ref: '#/components/responses/Unauthorized'

  /v1/saas/domains:
    get:
      operationId: listSaasDomains
      summary: Get SaaS Protection domains and backup statistics
      description: |
        Returns comprehensive information about all SaaS domains under protection, including
        backup statistics, seat utilization, and service health metrics across supported platforms.

        **🔧 Supported SaaS Platforms:**
        - **Microsoft 365**: Exchange Online, OneDrive, SharePoint, Teams
        - **Google Workspace**: Gmail, Google Drive, Calendar, Contacts, Team Drives
        - **Custom Applications**: Via API integration and connectors

        **📊 Comprehensive Domain Data:**
        - `backupStats`: Real-time backup success rates and service health
        - `seatsUsed`: Current seat allocation and utilization metrics
        - `productType`: Platform identifier (Office365, GoogleApps, etc.)
        - `retentionType`: Data retention policy (ICR, TBR, custom)
        - `externalSubscriptionId`: Platform-specific subscription identifier
        - Organizational mapping and billing information

        **📈 Backup Statistics Breakdown:**
        ```json
        {
          "backupStats": {
            "activeServicesCount": 1781,
            "activeServicesWithRecentBackupCount": 1780,
            "backupPercentage": 99.94
          }
        }
        ```

        **🎯 Business Applications:**
        - Multi-tenant SaaS protection overview
        - Client-level backup health monitoring
        - Seat utilization and billing optimization
        - Compliance reporting and audit trails
        - Service level agreement (SLA) tracking
        - Platform-specific backup analytics

        **💼 MSP Value Proposition:**
        - Unified multi-platform backup visibility
        - Automated client reporting capabilities
        - Proactive issue identification and resolution
        - Streamlined billing and seat management
        - Enhanced client communication and transparency
      tags:
        - SaaS Protection
      responses:
        '200':
          description: Successfully retrieved SaaS domains
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SaasDomain'
              examples:
                multi_platform_domains:
                  summary: Domains across multiple SaaS platforms
                  value:
                    - domain: "acmecorp.com"
                      saasCustomerId: 473121
                      saasCustomerName: "Acme Corporation"
                      productType: "Office365"
                      seatsUsed: 150
                      backupStats:
                        activeServicesCount: 150
                        activeServicesWithRecentBackupCount: 149
                        backupPercentage: 99.33
                      retentionType: "ICR"
                      externalSubscriptionId: "Classic:Office365:123456"
                    - domain: "beta-industries.com"
                      saasCustomerId: 427549
                      saasCustomerName: "Beta Industries"
                      productType: "GoogleApps"
                      seatsUsed: 75
                      backupStats:
                        activeServicesCount: 75
                        activeServicesWithRecentBackupCount: 75
                        backupPercentage: 100.0
        '403':
          $ref: '#/components/responses/Forbidden'

  /v1/itglue/{clientId}/dtc-assets:
    get:
      operationId: getItGlueDtcAssets
      summary: Get DTC backup status data for IT Glue integration
      description: |
        **🔗 IT Glue Integration Endpoint**

        ⚠️ **CRITICAL UNDERSTANDING**: This endpoint provides backup status data TO IT Glue
        for consumption within their documentation platform. It does NOT configure IT Glue
        settings or manage IT Glue data structures.

        **🎯 Integration Purpose:**
        - Export real-time backup health metrics to IT Glue
        - Provide unified protection status visibility within IT Glue interface
        - Enable MSPs to maintain centralized documentation with live backup data
        - Support compliance documentation and client reporting workflows

        **📊 Exported Data Structure:**
        - Asset backup status and health indicators
        - Last successful backup timestamps and frequency
        - Protection compliance metrics and SLA tracking
        - Storage utilization and capacity analytics
        - Screenshot verification status and visual proof
        - Alert summaries and issue identification
        - Network and system configuration details

        **💼 Business Value for MSPs:**
        - **Unified Dashboard**: Backup status directly within IT Glue documentation
        - **Client Communication**: Automated status updates in client-facing documents
        - **Compliance Reporting**: Real-time data for regulatory and audit requirements
        - **Operational Efficiency**: Reduced manual data entry and status tracking
        - **Proactive Support**: Early warning integration with existing ticketing workflows

        **🔧 Technical Implementation:**
        IT Glue polls this endpoint to retrieve current backup status and automatically
        updates documentation, flexible assets, and dashboard widgets with live data.

        **📈 Data Refresh Patterns:**
        - Real-time API calls for critical status updates
        - Scheduled polling for routine dashboard updates  
        - Event-driven updates for alert conditions
        - Configurable refresh intervals based on client requirements
      tags:
        - IT Glue
      parameters:
        - name: clientId
          in: path
          required: true
          description: Unique client organization identifier
          schema:
            type: integer
            minimum: 1
          example: 274558
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/perPage'
      responses:
        '200':
          description: Successfully retrieved backup status data for IT Glue
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/ItGlueBackupStatus'
                    description: Formatted backup status data for IT Glue consumption
                required:
                  - pagination
                  - items
              examples:
                it_glue_export:
                  summary: Backup status formatted for IT Glue
                  value:
                    pagination:
                      page: 1
                      perPage: 10
                      totalPages: 1
                      count: 5
                    items:
                      - name: "SQL-PROD-01"
                        status: "paired"
                        type: "dtc-servers"
                        screenshotSuccess: true
                        organizationName: "Acme Corp"
                        backupState: true
                        lastScreenshotUrl: "https://screenshots.datto.com/image.png"
                        lastBackup: **********
                        hostname: "SQL-PROD-01"
                        protectedVolume: *************
        '404':
          $ref: '#/components/responses/NotFound'

  /v1/report/activity-log:
    get:
      operationId: getActivityLogs
      summary: Get comprehensive activity logs for audit and compliance
      description: |
        Returns detailed, filterable activity logs for all actions and events within your Datto
        account infrastructure. Essential for compliance, security auditing, and operational
        troubleshooting across all platforms.

        **📋 Comprehensive Log Categories:**
        - **Backup Operations**: Start, success, failure, retry events with detailed timestamps
        - **Screenshot Verifications**: Attempt, success, failure status with image references
        - **Configuration Changes**: Device settings, protection policies, retention modifications
        - **User Actions**: Login attempts, permission changes, administrative operations
        - **Alert Events**: Trigger conditions, escalations, acknowledgments, resolutions
        - **System Events**: Service starts/stops, connectivity changes, maintenance windows

        **🔍 Advanced Filtering Capabilities:**
        - `clientName`: Filter events by specific client organization
        - `since` + `sinceUnits`: Flexible time range filtering (hours, days, weeks)
        - `target`: Focus on specific device, asset, or resource identifier
        - `targetType`: Filter by resource category (device, agent, share, etc.)
        - `user`: Track actions by specific user account or API key
        - `action`: Filter by specific operation types or event categories

        **🎯 Critical Use Cases:**
        - **Compliance Reporting**: SOX, HIPAA, PCI-DSS audit trail requirements
        - **Security Auditing**: Unauthorized access detection and forensic analysis
        - **Operational Troubleshooting**: Root cause analysis for backup failures
        - **Change Management**: Track configuration modifications and their impacts
        - **Performance Analysis**: Identify patterns in system behavior and utilization
        - **Client Reporting**: Transparent activity summaries for client communication

        **📊 Log Entry Structure:**
        ```json
        {
          "timestamp": "2025-06-06T14:30:00Z",
          "action": "backup.completed",
          "user": "<EMAIL>",
          "target": "SQL-SERVER-01",
          "targetType": "agent",
          "clientName": "Acme Corp",
          "success": true,
          "messageEN": "Backup completed successfully with screenshot verification"
        }
        ```

        **💡 Best Practices:**
        - Use time-based filtering to manage large log volumes
        - Implement regular log archival for long-term retention
        - Set up automated alerting for critical event patterns
        - Export logs to SIEM systems for advanced analysis
        - Maintain audit trails for compliance requirements
      tags:
        - Reporting
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/perPage'
        - name: clientName
          in: query
          description: Filter logs by specific client organization name
          schema:
            type: string
          example: "Acme Corporation"
        - name: since
          in: query
          description: Number of time units to look back from current time
          schema:
            type: integer
            minimum: 1
          example: 24
        - name: sinceUnits
          in: query
          description: Time unit for the 'since' parameter
          schema:
            type: string
            enum: [hours, days, weeks, months]
            default: hours
          example: "hours"
        - name: target
          in: query
          description: Specific target resource identifier (device serial, agent UUID, etc.)
          schema:
            type: string
          example: "7085C2F4782F"
        - name: targetType
          in: query
          description: Type of target resource to filter by
          schema:
            type: string
            enum: [device, agent, share, user, system]
          example: "device"
        - name: user
          in: query
          description: Filter by specific user email or API key identifier
          schema:
            type: string
          example: "<EMAIL>"
      responses:
        '200':
          description: Successfully retrieved activity logs
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    $ref: '#/components/schemas/Pagination'
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/ActivityLog'
                required:
                  - pagination
                  - items
              examples:
                audit_trail:
                  summary: Typical audit trail with various event types
                  value:
                    pagination:
                      page: 1
                      perPage: 10
                      totalPages: 15
                      count: 142
                    items:
                      - timestamp: "2025-06-06T14:30:00Z"
                        action: "backup.completed"
                        user: "<EMAIL>"
                        target: "SQL-SERVER-01"
                        targetType: "agent"
                        clientName: "Acme Corp"
                        success: true
                        messageEN: "Backup completed successfully"
                      - timestamp: "2025-06-06T14:25:00Z"
                        action: "screenshot.verified"
                        user: "<EMAIL>"
                        target: "SQL-SERVER-01"
                        targetType: "agent"
                        success: true
                        messageEN: "Screenshot verification completed successfully"
        '403':
          $ref: '#/components/responses/Forbidden'

components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: |
        HTTP Basic Authentication using API key pairs. Generate keys in the Datto Partner Portal.
        
        **Format**: `Authorization: Basic base64(PUBLIC_KEY:SECRET_KEY)`

  parameters:
    page:
      name: _page
      in: query
      description: Page number for pagination (1-based indexing)
      schema:
        type: integer
        minimum: 1
        default: 1
      example: 1

    perPage:
      name: _perPage
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        maximum: 1000
        default: 100
      example: 10

    serialNumber:
      name: serialNumber
      in: path
      required: true
      description: Device serial number identifier
      schema:
        type: string
        pattern: '^[A-Z0-9]{12}$'
      example: "7085C2F4782F"

  headers:
    X-API-Limit-Remaining:
      description: Number of requests remaining in current rate limit window
      schema:
        type: integer
      example: 9950

    X-API-Limit-Resets:
      description: Unix timestamp when rate limit window resets
      schema:
        type: integer
      example: 1640102400

    X-API-Limit-Cost:
      description: Cost of the current request
      schema:
        type: integer
      example: 1

  responses:
    Unauthorized:
      description: Authentication failed - Invalid API credentials
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Invalid API credentials"
            code: "UNAUTHORIZED"
            message: "Please check your API keys and ensure they are active"

    Forbidden:
      description: Access denied - Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Access denied"
            code: "FORBIDDEN"
            message: "Your account does not have permission to access this resource"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Resource not found"
            code: "NOT_FOUND"
            resource: "Device with serial number '7085C2F4782F' not found"

    RateLimitExceeded:
      description: Rate limit exceeded
      headers:
        X-API-Limit-Remaining:
          $ref: '#/components/headers/X-API-Limit-Remaining'
        X-API-Limit-Resets:
          $ref: '#/components/headers/X-API-Limit-Resets'
        Retry-After:
          description: Seconds to wait before retrying
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Rate limit exceeded"
            code: "RATE_LIMIT_EXCEEDED"
            message: "API rate limit of 10,000 requests/hour exceeded"
            retryAfter: 3600

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Internal server error"
            code: "INTERNAL_ERROR"
            requestId: "req_123456789"
            message: "An unexpected error occurred. Please contact support."

  schemas:
    # Base/Common Schemas
    Pagination:
      type: object
      description: Standard pagination information for list endpoints
      properties:
        page:
          type: integer
          minimum: 1
          description: Current page number (1-based)
          example: 1
        perPage:
          type: integer
          minimum: 1
          maximum: 1000
          description: Number of items per page
          example: 100
        totalPages:
          type: integer
          minimum: 0
          description: Total number of pages available
          example: 15
        count:
          type: integer
          minimum: 0
          description: Total number of items across all pages
          example: 1432
      required:
        - page
        - perPage
        - totalPages
        - count

    Storage:
      type: object
      description: Storage capacity information with units
      properties:
        size:
          type: integer
          format: int64
          minimum: 0
          description: Storage amount in specified units
          example: 1073741824
        units:
          type: string
          enum: [B, KB, MB, GB, TB, PB]
          description: Storage measurement units
          example: "GB"
      required:
        - size
        - units

    ErrorResponse:
      type: object
      description: Standard error response format
      properties:
        error:
          type: string
          description: Human-readable error message
          example: "Validation failed"
        code:
          type: string
          description: Machine-readable error code
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Detailed error description
          example: "The provided serial number format is invalid"
        requestId:
          type: string
          description: Unique request identifier for support
          example: "req_123456789"
        details:
          type: object
          description: Additional error context and details
          additionalProperties: true
      required:
        - error
        - code

    # BCDR Schemas
    BcdrDevice:
      type: object
      description: Complete BCDR device information with health and status
      properties:
        serialNumber:
          type: string
          pattern: '^[A-Z0-9]{12}$'
          description: Unique device serial number identifier
          example: "7085C2F4782F"
        name:
          type: string
          maxLength: 100
          description: User-defined device name
          example: "PRIMARY-BACKUP-01"
        model:
          type: string
          enum: [SIRIS, ALTO, NAS, S5-X, S5-2, S5-12, S5-48, S4B1, CLDSIRIS, EB4S, EBDR]
          description: Device model and type identifier
          example: "S5-X"
        region:
          type: object
          nullable: true
          description: Geographic region for cloud devices
          properties:
            location:
              type: string
              example: "East US"
            provider:
              type: string
              enum: [azure, aws, gcp]
              example: "azure"
        lastSeenDate:
          type: string
          format: date-time
          description: Last communication with device (ISO 8601)
          example: "2025-06-06T10:54:26-04:00"
        hidden:
          type: boolean
          description: Whether device is hidden in normal views
          example: false
        activeTickets:
          type: integer
          minimum: 0
          description: Number of active support tickets
          example: 0
        servicePlan:
          type: string
          nullable: true
          description: Assigned service plan name
          example: "Infinite Cloud Retention"
        registrationDate:
          type: string
          format: date-time
          description: Device registration timestamp (ISO 8601)
          example: "2024-01-05T12:14:44-05:00"
        servicePeriod:
          type: string
          format: date-time
          description: Service period expiration (ISO 8601)
          example: "2025-07-01T00:00:00-04:00"
        warrantyExpire:
          type: string
          format: date-time
          nullable: true
          description: Hardware warranty expiration (ISO 8601)
          example: "2026-04-26T00:00:00-04:00"
        localStorageUsed:
          allOf:
            - $ref: '#/components/schemas/Storage'
          nullable: true
          description: Local storage utilization
        localStorageAvailable:
          allOf:
            - $ref: '#/components/schemas/Storage'
          nullable: true
          description: Available local storage capacity
        offsiteStorageUsed:
          allOf:
            - $ref: '#/components/schemas/Storage'
          nullable: true
          description: Cloud storage utilization
        totalManagedDisk:
          allOf:
            - $ref: '#/components/schemas/Storage'
          nullable: true
          description: Total managed disk space (cloud devices)
        protectedSpace:
          allOf:
            - $ref: '#/components/schemas/Storage'
          nullable: true
          description: Total protected data volume
        internalIP:
          type: string
          format: ipv4
          description: Device internal network IP address
          example: "*************"
        resellerCompanyName:
          type: string
          description: Reseller partner organization name
          example: "TechPartner MSP"
        clientCompanyName:
          type: string
          description: End client organization name  
          example: "Acme Corporation"
        organizationName:
          type: string
          description: Primary organization name
          example: "Acme Corporation"
        organizationId:
          type: integer
          minimum: 1
          description: Unique organization identifier
          example: 274558
        agentCount:
          type: integer
          minimum: 0
          description: Number of protected agents
          example: 25
        shareCount:
          type: integer
          minimum: 0
          description: Number of protected shares
          example: 3
        alertCount:
          type: integer
          minimum: 0
          description: Number of active alerts
          example: 0
        uptime:
          type: integer
          minimum: 0
          description: Device uptime in seconds
          example: 2104517
        remoteWebUrl:
          type: string
          format: uri
          description: Direct URL to device web interface
          example: "https://portal.dattobackup.com/device/7085C2F4782F/remote-web"
      required:
        - serialNumber
        - name
        - model
        - lastSeenDate
        - hidden
        - activeTickets
        - organizationName
        - organizationId
        - agentCount
        - shareCount
        - alertCount
        - uptime

    BcdrClient:
      type: object
      description: Client organization with protected agents
      properties:
        clientName:
          type: string
          description: Client organization display name
          example: "Acme Corporation"
        organizationId:
          type: integer
          minimum: 1
          description: Unique organization identifier
          example: 274558
        organizationName:
          type: string
          description: Formal organization name
          example: "Acme Corporation"
        agents:
          type: array
          items:
            $ref: '#/components/schemas/BcdrAgent'
          description: List of protected agents for this client
      required:
        - clientName
        - organizationId
        - organizationName
        - agents

    BcdrAgent:
      type: object
      description: Protected agent with backup and verification status
      properties:
        uuid:
          type: string
          format: uuid
          description: Universally unique identifier for agent
          example: "389d17d5d6fd4647b3dc0fba948d78e5"
        shortCode:
          type: string
          pattern: '^[A-Z0-9]{9}$'
          description: Human-readable agent identifier
          example: "85WTF6ZK5"
        type:
          type: string
          enum: [cc4pc, cc4server, share, vm]
          description: Agent type classification
          example: "cc4pc"
        hostname:
          type: string
          maxLength: 253
          description: System hostname or FQDN
          example: "SQL-SERVER-01"
        lastSnapshot:
          type: integer
          format: int64
          description: Last backup timestamp (Unix)
          example: 1749142382
        lastScreenshot:
          type: integer
          format: int64
          nullable: true
          description: Last screenshot attempt timestamp (Unix)
          example: 1749084722
        screenshotSuccess:
          type: boolean
          description: Screenshot verification success status
          example: true
        protectedMachine:
          $ref: '#/components/schemas/ProtectedMachine'
      required:
        - uuid
        - shortCode
        - type
        - hostname
        - lastSnapshot
        - screenshotSuccess
        - protectedMachine

    ProtectedMachine:
      type: object
      description: Hardware identification for protected machine
      properties:
        serial:
          type: string
          nullable: true
          description: Hardware serial number
          example: "VMware-56 4d 12 34 56 78 9a bc-de fg hi jk lm no pq rs"
        mac:
          type: string
          nullable: true
          pattern: '^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
          description: Primary network interface MAC address
          example: "00:50:56:84:3e:c1"

    DeviceAgent:
      type: object
      description: Detailed agent information from device perspective
      properties:
        name:
          type: string
          description: Agent display name
          example: "SQL-SERVER-01"
        os:
          type: string
          nullable: true
          description: Operating system identification
          example: "Windows Server 2019 Standard"
        hardDrives:
          type: array
          items:
            $ref: '#/components/schemas/HardDrive'
          description: Physical and virtual drives
        wasOffloadedOnSupernode:
          type: boolean
          description: Whether agent was offloaded to supernode
          example: false
      required:
        - name

    DeviceShare:
      type: object
      description: Network share or file system backup
      properties:
        name:
          type: string
          description: Share name or path
          example: "/mnt/data"
      required:
        - name

    HardDrive:
      type: object
      description: Storage device information
      properties:
        name:
          type: string
          description: Drive identifier or mount point
          example: "C:\\"
        totalSpace:
          $ref: '#/components/schemas/Storage'
        usedSpace:
          $ref: '#/components/schemas/Storage'
      required:
        - name
        - totalSpace
        - usedSpace

    DeviceAsset:
      type: object
      description: Comprehensive asset information with backup details
      properties:
        name:
          type: string
          description: Asset display name
          example: "SQL-SERVER-01"
        assetId:
          type: integer
          minimum: 1
          description: Unique asset identifier
          example: 123456
        type:
          type: string
          enum: [agent, share]
          description: Asset type classification
          example: "agent"
        volume:
          type: string
          nullable: true
          pattern: '^[a-f0-9]{32}$'
          description: Volume identifier hash
          example: "4b65219643aa4bc78e359928ead7fab5"
        localIp:
          type: string
          format: ipv4
          nullable: true
          description: Local network IP address
          example: "************"
        os:
          type: string
          nullable: true
          description: Operating system details
          example: "Windows Server 2019 Standard"
        protectedVolumesCount:
          type: integer
          minimum: 0
          description: Number of protected volumes
          example: 3
        unprotectedVolumesCount:
          type: integer
          minimum: 0
          description: Number of unprotected volumes
          example: 0
        protectedVolumeNames:
          type: array
          items:
            type: string
          description: Names/paths of protected volumes
          example: ["C:\\", "D:\\Data"]
        unprotectedVolumeNames:
          type: array
          items:
            type: string
          description: Names/paths of unprotected volumes
        agentVersion:
          type: string
          nullable: true
          pattern: '^\d+\.\d+\.\d+\.\d+$'
          description: Datto agent software version
          example: "*******"
        isPaused:
          type: boolean
          description: Whether backup is currently paused
          example: false
        isArchived:
          type: boolean
          description: Whether asset is archived
          example: false
        latestOffsite:
          type: integer
          format: int64
          nullable: true
          description: Most recent offsite backup timestamp (Unix)
          example: 1584817262
        localSnapshots:
          type: integer
          minimum: 0
          description: Number of local backup snapshots
          example: 48
        lastSnapshot:
          type: integer
          format: int64
          description: Most recent backup timestamp (Unix)
          example: 1584817262
        lastScreenshotAttempt:
          type: integer
          format: int64
          description: Last screenshot attempt timestamp (Unix)
          example: 1584817262
        lastScreenshotAttemptStatus:
          type: boolean
          description: Success status of last screenshot attempt
          example: true
        lastScreenshotUrl:
          type: string
          format: uri
          description: Direct URL to most recent screenshot image
          example: "https://device.dattobackup.com/sirisReporting/image/latest/abc123.png"
        fqdn:
          type: string
          nullable: true
          maxLength: 253
          description: Fully qualified domain name
          example: "sql-server-01.acme.local"
        backups:
          type: array
          items:
            $ref: '#/components/schemas/Snapshot'
          description: Recent backup history with verification
      required:
        - name
        - assetId
        - isPaused
        - isArchived
        - localSnapshots
        - lastSnapshot

    Alert:
      type: object
      description: System alert or notification
      properties:
        type:
          type: string
          description: Alert category and type
          example: "Device Not Seen Alert"
        dateSent:
          type: string
          format: date-time
          description: Alert notification timestamp (ISO 8601)
          example: "2025-06-06T14:30:00+00:00"
        dateTriggered:
          type: string
          format: date-time
          description: Alert trigger timestamp (ISO 8601)
          example: "2025-06-06T14:25:00+00:00"
        threshold:
          type: integer
          minimum: 0
          description: Alert threshold value
          example: 60
        unit:
          type: string
          description: Threshold measurement unit
          example: "Minutes"
      required:
        - type
        - dateSent
        - dateTriggered

    Snapshot:
      type: object
      description: Individual backup with verification details
      properties:
        timestamp:
          type: string
          format: date-time
          description: Backup completion timestamp (ISO 8601)
          example: "2025-06-06T14:01:03+00:00"
        backup:
          $ref: '#/components/schemas/Backup'
        localVerification:
          $ref: '#/components/schemas/LocalVerification'
        advancedVerification:
          $ref: '#/components/schemas/AdvancedVerification'
      required:
        - timestamp
        - backup

    Backup:
      type: object
      description: Core backup operation result
      properties:
        status:
          type: string
          enum: [success, failed, warning, running]
          description: Backup completion status
          example: "success"
        errorMessage:
          type: string
          nullable: true
          description: Error details if backup failed
          example: null
      required:
        - status

    LocalVerification:
      type: object
      description: Local backup integrity verification
      properties:
        status:
          type: string
          enum: [passed, failed, skipped, running]
          description: Local verification result
          example: "passed"
        errors:
          type: array
          items:
            $ref: '#/components/schemas/LocalVerificationError'
          description: Verification error details
      required:
        - status
        - errors

    LocalVerificationError:
      type: object
      description: Specific verification error
      properties:
        errorType:
          type: string
          description: Classification of verification error
          example: "checksum_mismatch"
        errorMessage:
          type: string
          nullable: true
          description: Human-readable error description
          example: "File checksum validation failed"
      required:
        - errorType

    AdvancedVerification:
      type: object
      description: Advanced verification including screenshots
      properties:
        screenshotVerification:
          $ref: '#/components/schemas/ScreenshotVerification'

    ScreenshotVerification:
      type: object
      description: Screenshot verification result
      properties:
        status:
          type: string
          enum: [successful, failed, pending, skipped]
          description: Screenshot verification status
          example: "successful"
        image:
          type: string
          format: uri
          description: URL to verification screenshot
          example: "https://device.dattobackup.com/image.png"
      required:
        - status
        - image

    # DTC Schemas
    DtcAsset:
      type: object
      description: Direct-to-Cloud asset with comprehensive status
      properties:
        name:
          type: string
          maxLength: 100
          description: Asset display name
          example: "CLOUD-SQL-01"
        status:
          type: string
          enum: [paired, unpaired, error, pending, delete-error]
          description: Asset pairing and operational status
          example: "paired"
        type:
          type: string
          enum: [dtc-servers, dtceis, dtc-workstations]
          description: Asset type classification
          example: "dtc-servers"
        screenshotSuccess:
          type: boolean
          description: Screenshot verification success status
          example: true
        organizationId:
          type: integer
          minimum: 1
          description: Organization identifier
          example: 274558
        organizationName:
          type: string
          description: Organization name
          example: "Acme Corporation"
        resellerId:
          type: integer
          minimum: 1
          description: Reseller partner identifier
          example: 54803
        backupState:
          type: boolean
          description: Current backup operational state
          example: true
        lastScreenshotUrl:
          type: string
          format: uri
          description: Direct URL to latest screenshot
          example: "https://device.dattobackup.com/image.png"
        assetUuid:
          type: string
          format: uuid
          description: Unique asset identifier
          example: "9c8735c3035e41c4a75e0afc80dd71a7"
        lastBackup:
          type: integer
          format: int64
          description: Last successful backup timestamp (Unix)
          example: **********
        last10Backups:
          type: array
          items:
            $ref: '#/components/schemas/BackupHistory'
          maxItems: 10
          description: Recent backup attempt history
        hostname:
          type: string
          maxLength: 253
          description: System hostname
          example: "CLOUD-SQL-01"
        macAddress:
          type: string
          pattern: '^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})
          description: Primary network interface MAC address
          example: "00:50:56:84:3e:c1"
        externalIp:
          type: string
          format: ipv4
          description: External/public IP address
          example: "************"
        smbiosVendor:
          type: string
          description: System BIOS vendor identification
          example: "VMware, Inc."
        smbiosName:
          type: string
          description: System BIOS product name
          example: "VMware Virtual Platform"
        agentCode:
          type: string
          pattern: '^[A-Z0-9]{9}
          description: Agent identification code
          example: "UC52A89RV"
        protectedVolume:
          type: integer
          format: int64
          minimum: 0
          description: Total protected storage in bytes
          example: 63845695488
      required:
        - name
        - status
        - type
        - screenshotSuccess
        - organizationId
        - organizationName
        - resellerId
        - backupState
        - assetUuid
        - lastBackup
        - last10Backups
        - hostname
        - macAddress
        - externalIp
        - agentCode
        - protectedVolume

    BackupHistory:
      type: object
      description: Individual backup attempt record
      properties:
        timestamp:
          type: integer
          format: int64
          description: Backup attempt timestamp (Unix)
          example: **********
        backupStatus:
          type: boolean
          description: Backup success indicator
          example: true
        screenshotSuccess:
          type: boolean
          nullable: true
          description: Screenshot verification result
          example: true
      required:
        - timestamp
        - backupStatus

    # SaaS Protection Schemas
    SaasDomain:
      type: object
      description: SaaS domain with protection statistics
      properties:
        domain:
          type: string
          maxLength: 253
          description: Protected domain name
          example: "acmecorp.com"
        saasCustomerId:
          type: integer
          minimum: 1
          description: SaaS customer identifier
          example: 473121
        saasCustomerName:
          type: string
          description: SaaS customer display name
          example: "Acme Corporation"
        seatsUsed:
          type: integer
          minimum: 0
          description: Current seat utilization count
          example: 150
        productType:
          type: string
          enum: [Office365, GoogleApps, Salesforce, Box, Dropbox]
          description: SaaS platform type
          example: "Office365"
        retentionType:
          type: string
          enum: [ICR, TBR, Custom]
          description: Data retention policy type
          example: "ICR"
        externalSubscriptionId:
          type: string
          description: Platform-specific subscription identifier
          example: "Classic:Office365:123456"
        organizationId:
          type: integer
          minimum: 1
          description: Organization identifier
          example: 373135
        organizationName:
          type: string
          description: Organization name
          example: "Acme Corporation"
        backupStats:
          $ref: '#/components/schemas/BackupStats'
      required:
        - domain
        - saasCustomerId
        - saasCustomerName
        - seatsUsed
        - productType
        - retentionType
        - externalSubscriptionId
        - organizationId
        - organizationName
        - backupStats

    BackupStats:
      type: object
      description: Aggregated backup statistics
      properties:
        activeServicesCount:
          type: integer
          minimum: 0
          description: Total number of active services
          example: 150
        activeServicesWithRecentBackupCount:
          type: integer
          minimum: 0
          description: Services with recent successful backups
          example: 149
        backupPercentage:
          type: number
          format: float
          minimum: 0
          maximum: 100
          description: Overall backup success percentage
          example: 99.33
      required:
        - activeServicesCount
        - activeServicesWithRecentBackupCount
        - backupPercentage

    # IT Glue Integration Schemas
    ItGlueBackupStatus:
      type: object
      description: Backup status data formatted for IT Glue consumption
      properties:
        name:
          type: string
          description: Asset name for IT Glue display
          example: "SQL-PROD-01"
        status:
          type: string
          enum: [paired, unpaired, error, pending]
          description: Asset operational status
          example: "paired"
        type:
          type: string
          description: Asset type classification
          example: "dtc-servers"
        screenshotSuccess:
          type: boolean
          description: Screenshot verification status
          example: true
        organizationName:
          type: string
          description: Client organization name
          example: "Acme Corp"
        backupState:
          type: boolean
          description: Current backup state
          example: true
        lastScreenshotUrl:
          type: string
          format: uri
          description: Screenshot URL for IT Glue embedding
          example: "https://screenshots.datto.com/image.png"
        lastBackup:
          type: integer
          format: int64
          description: Last backup timestamp for IT Glue display
          example: **********
        hostname:
          type: string
          description: System hostname
          example: "SQL-PROD-01"
        protectedVolume:
          type: integer
          format: int64
          description: Protected data volume in bytes
          example: *************
        complianceStatus:
          type: string
          enum: [compliant, warning, critical]
          description: Overall compliance status for IT Glue
          example: "compliant"
        lastVerified:
          type: integer
          format: int64
          nullable: true
          description: Last verification timestamp
          example: **********
      required:
        - name
        - status
        - type
        - screenshotSuccess
        - organizationName
        - backupState
        - hostname

    # Reporting Schemas
    ActivityLog:
      type: object
      description: Detailed activity log entry
      properties:
        timestamp:
          type: string
          format: date-time
          description: Event occurrence timestamp (ISO 8601)
          example: "2025-06-06T14:30:00Z"
        action:
          type: string
          description: Specific action or event type
          example: "backup.completed"
        user:
          type: string
          description: User account or system identifier
          example: "<EMAIL>"
        target:
          type: string
          description: Target resource identifier
          example: "SQL-SERVER-01"
        targetType:
          type: string
          enum: [device, agent, share, user, system, organization]
          description: Type of target resource
          example: "agent"
        targetDisplayName:
          type: string
          description: Human-readable target name
          example: "SQL Server Production"
        clientName:
          type: string
          nullable: true
          description: Client organization name
          example: "Acme Corporation"
        success:
          type: boolean
          description: Operation success indicator
          example: true
        messageEN:
          type: string
          description: English language event description
          example: "Backup completed successfully with screenshot verification"
        interface:
          type: string
          enum: [Portal, API, System, Agent]
          description: Interface used for the action
          example: "System"
        ipAddress:
          type: string
          format: ipv4
          nullable: true
          description: Source IP address
          example: "************"
        requestId:
          type: string
          description: Unique request identifier for correlation
          example: "req_123456789"
        userRoles:
          type: array
          items:
            type: string
          description: User roles at time of action
          example: ["admin", "backup_operator"]
        details:
          type: object
          additionalProperties: true
          description: Additional event-specific details
      required:
        - timestamp
        - action
        - user
        - target
        - targetType
        - success
        - messageEN