#!/usr/bin/env python3
"""
Test MCP server connection on macOS
"""
import subprocess
import json
import sys
import os
import time

def find_python():
    """Find the correct Python executable"""
    candidates = [
        "/Users/<USER>/webdev/MCPs/DPP/datto-mcp-server/venv/bin/python",
        "/Users/<USER>/webdev/MCPs/DPP/datto-mcp-server/venv/bin/python3",
        "python3",
        "python"
    ]
    
    for python in candidates:
        try:
            result = subprocess.run([python, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ Found Python: {python}")
                print(f"  Version: {result.stdout.strip()}")
                return python
        except:
            continue
    
    return None

def test_mcp_init():
    """Test MCP initialization"""
    python = find_python()
    if not python:
        print("❌ Could not find Python executable")
        return False
    
    # Test initialization message
    init_message = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "0.1.0",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        },
        "id": 1
    }
    
    # Set up environment
    env = os.environ.copy()
    env.update({
        "DATTO_PUBLIC_KEY": "your_public_key",
        "DATTO_SECRET_KEY": "your_secret_key",
        "PYTHONUNBUFFERED": "1"
    })
    
    # Start the MCP server
    print("\nStarting MCP server...")
    proc = subprocess.Popen(
        [python, "/Users/<USER>/webdev/MCPs/DPP/datto-mcp-server/mcp_server.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        env=env,
        cwd="/Users/<USER>/webdev/MCPs/DPP/datto-mcp-server",
        text=True
    )
    
    # Send initialization
    try:
        proc.stdin.write(json.dumps(init_message) + "\n")
        proc.stdin.flush()
        
        # Read response with timeout
        import select
        ready, _, _ = select.select([proc.stdout], [], [], 5.0)
        
        if ready:
            response = proc.stdout.readline()
            print(f"\nResponse: {response}")
            
            try:
                resp_json = json.loads(response)
                if "result" in resp_json:
                    print("✓ MCP server initialized successfully!")
                    return True
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
        else:
            print("❌ No response from server (timeout)")
            
        # Check stderr for errors
        stderr_output = proc.stderr.read()
        if stderr_output:
            print(f"\nServer errors:\n{stderr_output}")
            
    finally:
        proc.terminate()
        proc.wait()
    
    return False

def generate_correct_config():
    """Generate the correct Claude Desktop config"""
    print("\n" + "="*50)
    print("CORRECT CLAUDE DESKTOP CONFIG FOR MACOS")
    print("="*50)
    
    config = {
        "mcpServers": {
            "datto-api": {
                "command": "/Users/<USER>/webdev/MCPs/DPP/datto-mcp-server/venv/bin/python",
                "args": ["/Users/<USER>/webdev/MCPs/DPP/datto-mcp-server/mcp_server.py"],
                "cwd": "/Users/<USER>/webdev/MCPs/DPP/datto-mcp-server",
                "env": {
                    "PYTHONUNBUFFERED": "1"
                }
            }
        }
    }
    
    print(json.dumps(config, indent=2))
    
    print("\nIMPORTANT NOTES:")
    print("1. Use the full path to Python in the venv")
    print("2. Set cwd to the project directory, NOT the venv directory")
    print("3. Make sure your .env file exists in the project directory")
    print("4. The .env file should contain:")
    print("   DATTO_PUBLIC_KEY=your_key")
    print("   DATTO_SECRET_KEY=your_secret")

if __name__ == "__main__":
    print("Testing MCP Server Connection on macOS")
    print("="*50)
    
    # Test Python discovery
    python = find_python()
    
    if python:
        # Test MCP protocol
        if test_mcp_init():
            print("\n✅ MCP server is working!")
        else:
            print("\n❌ MCP server failed to initialize")
    
    # Show correct config
    generate_correct_config()