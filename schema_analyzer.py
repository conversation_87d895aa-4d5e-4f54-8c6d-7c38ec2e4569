#!/usr/bin/env python3
"""
Schema analyzer for extracting OpenAPI schemas from responses
"""
import re
from typing import Any, Dict, List, Set


class SchemaAnalyzer:
    """Analyzes API responses and extracts OpenAPI schemas"""
    
    def extract_schema(self, data: Any, depth: int = 0) -> Dict[str, Any]:
        """Extract OpenAPI schema from response data"""
        if depth > 10:
            return {'type': 'object'}
        
        if data is None:
            return {'type': 'null', 'nullable': True}
        
        if isinstance(data, bool):
            return {'type': 'boolean', 'example': data}
        
        if isinstance(data, int):
            return self._analyze_integer(data)
        
        if isinstance(data, float):
            return {'type': 'number', 'format': 'float', 'example': data}
        
        if isinstance(data, str):
            return self._analyze_string(data)
        
        if isinstance(data, list):
            return self._analyze_array(data, depth)
        
        if isinstance(data, dict):
            return self._analyze_object(data, depth)
        
        return {'type': 'string', 'description': f'Unknown type: {type(data).__name__}'}
    
    def _analyze_integer(self, value: int) -> Dict[str, Any]:
        """Analyze integer values for format hints"""
        schema = {'type': 'integer', 'example': value}
        
        # Detect timestamps
        if 1000000000 < value < 2000000000:
            schema['format'] = 'timestamp'
            schema['description'] = 'Unix timestamp'
        elif value > 2147483647:
            schema['format'] = 'int64'
        
        return schema
    
    def _analyze_string(self, value: str) -> Dict[str, Any]:
        """Analyze string values for format hints"""
        schema = {'type': 'string'}
        
        if len(value) <= 200:
            schema['example'] = value
        
        # Format detection
        if re.match(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', value):
            schema['format'] = 'date-time'
        elif re.match(r'^[a-fA-F0-9-]{36}$', value):
            schema['format'] = 'uuid'
        elif re.match(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$', value):
            schema['format'] = 'ipv4'
        elif '@' in value and '.' in value:
            schema['format'] = 'email'
        elif value.startswith(('http://', 'https://')):
            schema['format'] = 'uri'
            if any(ext in value for ext in ['.png', '.jpg', '.jpeg', '.gif']):
                schema['description'] = 'Image URL'
        
        return schema
    
    def _analyze_array(self, data: List[Any], depth: int) -> Dict[str, Any]:
        """Analyze array values"""
        if not data:
            return {'type': 'array', 'items': {}}
        
        # Analyze multiple items to find common schema
        item_schemas = []
        for item in data[:5]:  # Sample first 5 items
            item_schemas.append(self.extract_schema(item, depth + 1))
        
        # Merge schemas
        merged = self._merge_schemas(item_schemas)
        
        return {
            'type': 'array',
            'items': merged,
            'minItems': 0,
            'maxItems': len(data) if len(data) < 1000 else None
        }
    
    def _analyze_object(self, data: Dict[str, Any], depth: int) -> Dict[str, Any]:
        """Analyze object values"""
        properties = {}
        required = []
        
        for key, value in data.items():
            prop_schema = self.extract_schema(value, depth + 1)
            properties[key] = prop_schema
            
            # Non-null values are likely required
            if value is not None:
                required.append(key)
        
        schema = {
            'type': 'object',
            'properties': properties
        }
        
        if required:
            schema['required'] = sorted(required)
        
        # Add example if small enough
        if len(str(data)) < 500:
            schema['example'] = data
        
        return schema
    
    def _merge_schemas(self, schemas: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge multiple schemas intelligently"""
        if not schemas:
            return {}
        
        if len(schemas) == 1:
            return schemas[0]
        
        # Check if all same type
        types = set(s.get('type') for s in schemas if s)
        if len(types) == 1:
            merged = {'type': list(types)[0]}
            
            if merged['type'] == 'object':
                # Merge object properties
                all_props = {}
                common_required = None
                
                for schema in schemas:
                    for prop, prop_schema in schema.get('properties', {}).items():
                        if prop not in all_props:
                            all_props[prop] = prop_schema
                    
                    # Find common required fields
                    if 'required' in schema:
                        req_set = set(schema['required'])
                        if common_required is None:
                            common_required = req_set
                        else:
                            common_required &= req_set
                
                merged['properties'] = all_props
                if common_required:
                    merged['required'] = sorted(list(common_required))
            
            return merged
        
        # Different types - use oneOf
        return {'oneOf': schemas}
    
    def get_all_fields(self, data: Any, prefix: str = '') -> Set[str]:
        """Get all field paths in data"""
        fields = set()
        
        if isinstance(data, dict):
            for key, value in data.items():
                field_path = f"{prefix}.{key}" if prefix else key
                fields.add(field_path)
                fields.update(self.get_all_fields(value, field_path))
        elif isinstance(data, list) and data:
            # Sample first item
            fields.update(self.get_all_fields(data[0], f"{prefix}[]"))
        
        return fields
    
    def detect_special_fields(self, data: Any) -> Dict[str, bool]:
        """Detect special field types in data"""
        data_str = str(data).lower()
        
        return {
            'has_screenshot_data': any(term in data_str for term in [
                'screenshot', 'screenshoturl', 'lastscreenshot', 'image'
            ]),
            'has_verification_data': any(term in data_str for term in [
                'verification', 'verify', 'validated', 'advancedverification'
            ]),
            'has_backup_data': any(term in data_str for term in [
                'backup', 'lastbackup', 'snapshot', 'backupstatus'
            ]),
            'has_timestamp_data': any(term in data_str for term in [
                'timestamp', 'created', 'updated', 'lastseen'
            ])
        }